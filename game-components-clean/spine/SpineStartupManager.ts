/*
 * SpineStartupManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SpineStartupManager')
export class SpineStartupManager extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        onLoad
    }

    start() {
        this.initializeOnStart&&this.initializeSpineSystem()
    }

    initializeSpineSystem() {
        var e=a(o().mark((function e(){
            return o().wrap((function(e){
            for(;
        ;
        )switch(e.prev=e.next){
            case 0:if(!this._isInitialized){
            e.next=3;
        break
    }

}
