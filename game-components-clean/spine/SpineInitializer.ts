/*
 * SpineInitializer - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SpineInitializer')
export class SpineInitializer extends Component {

    // Extracted methods (may need manual cleanup)
    getInstance() {
        return this._instance
    }

    onLoad() {
        null===r._instance?(r._instance=this,this.initializeSpine()):this.destroy()
    }

    onDestroy() {
        r._instance===this&&(r._instance=null)
    }

    initializeSpine() {
        var i=e(t().mark((function i(){
            return t().wrap((function(i){
            for(;
        ;
        )switch(i.prev=i.next){
            case 0:if(!r._isSpineLoaded){
            i.next=3;
        break
    }

}
