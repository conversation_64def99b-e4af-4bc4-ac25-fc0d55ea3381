/*
 * Auth<PERSON>anager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('AuthManager')
export class AuthManager extends Component {

    // Extracted methods (may need manual cleanup)
    getInstance() {
        return this._instance
    }

    start() {
        n._instance=this,console.log("[AuthManager] Initialized")
    }

    extractTokenFromURL() {
        try{
            var t=new URLSearchParams(window.location.search).get("token");
        return t?(console.log("[AuthManager] Token extracted from URL"),decodeURIComponent(t)):(console.log("[AuthManager] No token found in URL parameters"),"")
    }

    initializeWithToken() {
        return t&&0!==t.trim().length?(console.log("[AuthManager] Initializing with provided token"),this.initializeAuth(t.trim(),e||this._config.defaultPlayerId)):{
            success:!1,error:"Token parameter is required"
    }

    extractTokenFromParameter() {
        try{
            return t.startsWith("token=")?t.substring(6):t
    }

    validateToken() {
        if(!t||0===t.trim().length)return{
            valid:!1,error:"Token is empty"
    }

    decodeToken() {
        try{
            var e=atob(t);
        try{
            return{
            success:!0,data:JSON.parse(e)
    }

    initializeAuth() {
        var a=this.validateToken(t);
        return a.valid?(this._authData.token=t,this._authData.playerId=e||"",this._authData.isAuthenticated=!1,console.log("[AuthManager] Authentication initialized"),{
            success:!0
    }

    setAuthenticationStatus() {
        this._authData.isAuthenticated=t,e&&(this._authData.sessionId=e),void 0!==a&&(this._authData.balance=a),console.log("[AuthManager] Authentication status updated:",t)
    }

    updateBalance() {
        this._authData.balance=t,console.log("[AuthManager] Balance updated:",t)
    }

    updateUserProfile() {
        t&&(this._authData.userProfile=t,this._authData.playerId=t.id||this._authData.playerId,"number"==typeof t.balance&&(this._authData.balance=t.balance)),e&&(this._authData.bettingLimits=e),console.log("[AuthManager] User profile updated:",{
            playerId:this._authData.playerId,nickname:null==t?void 0:t.nickname,balance:this._authData.balance,currency:null==t?void 0:t.currency
    }

    getAuthData() {
        return a({
    }

    getToken() {
        return this._authData.token
    }

    isAuthenticated() {
        return this._authData.isAuthenticated&&this._authData.token.length>0
    }

    getUserProfile() {
        return this._authData.userProfile
    }

    getBettingLimits() {
        return this._authData.bettingLimits
    }

    getBalance() {
        return this._authData.balance
    }

    getUserNickname() {
        return(null==(t=this._authData.userProfile)?void 0:t.nickname)||this._authData.playerId||"Unknown"
    }

    clearAuth() {
        this._authData={
            token:"",playerId:"",sessionId:"",balance:0,isAuthenticated:!1
    }

    getAuthHeaders() {
        return{
            Authorization:"Bearer "+this._authData.token,"X-Player-ID":this._authData.playerId,"X-Session-ID":this._authData.sessionId
    }

    createAuthPayload() {
        return{
            token:this._authData.token,playerId:this._authData.playerId,sessionId:this._authData.sessionId,timestamp:(new Date).toISOString()
    }

    autoInitialize() {
        var t=this.extractTokenFromURL();
        if(t)return console.log("[AuthManager] Using token from URL"),this.initializeAuth(t);
        if(console.log("[AuthManager] No token found in URL parameters, trying default token"),this._config.defaultToken){
            console.log("[AuthManager] Using default token");
        var e=decodeURIComponent(this._config.defaultToken),a=this.initializeAuth(e);
        return a.success?a:(console.error("[AuthManager] Default token validation failed:",a.error),{
            success:!1,error:"Default token validation failed: "+a.error
    }

    validateSession() {
        return!(!this._authData.token||!this._authData.isAuthenticated)&&this._authData.sessionId.length>0
    }

}
