/*
 * EventManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('EventManager')
export class EventManager extends Component {

    // Extracted methods (may need manual cleanup)
    emit() {
        this.eventTarget.emit(e,t)
    }

    on() {
        this.eventTarget.on(e,t,n)
    }

    off() {
        this.eventTarget.off(e,t,n)
    }

    return e.instance() {
        return e.instance
    }

}
