/*
 * GameManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('GameManager')
export class GameManager extends Component {

    // Extracted methods (may need manual cleanup)
    mapSymbolToPlayer() {
        if(!e)return null;
        var t=e.trim();
        if(!t)return null;
        switch(t.toUpperCase()){
            case"아":case"A":case"PLAYER_A":return G.A;
        case"파":case"P":case"PLAYER_P":return G.P;
        case"트":case"T":case"PLAYER_T":return G.T;
        default:return null
    }

    getInstance() {
        return this._instance
    }

    start() {
        if(t._instance=this,this.privateInitializeManagers(),!this.privateSetupEventListeners())return console.log("[GameManager] Managers not ready, scheduling retry..."),void this.privateRetryInitialization(1);
        this.privateStartGame()
    }

    StartWithToken() {
        if(console.log("[GameManager] Starting game with provided token..."),!e||0===e.trim().length)return console.error("[GameManager] Token parameter is required"),void this.eventTarget.emit("token-required",{
            message:"Token parameter is required to start the game",suggestion:"Please provide a valid authentication token"
    }

    startGame() {
        var e=i(s().mark((function e(){
            return s().wrap((function(e){
            for(;
        ;
        )switch(e.prev=e.next){
            case 0:if(e.prev=0,console.log("[GameManager] Starting game..."),this.authManager.autoInitialize().success){
            e.next=8;
        break
    }

}
