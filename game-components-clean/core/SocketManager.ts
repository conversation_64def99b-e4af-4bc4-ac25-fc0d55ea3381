/*
 * SocketManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SocketManager')
export class SocketManager extends Component {

    // Extracted methods (may need manual cleanup)
    start() {
        a._instance=this,this.privateSetupFocusManagement(),console.log("[SocketManager] Initialized")
    }

    GetInstance() {
        return this._instance
    }

    ConnectWithToken() {
        var n=this;
        return void 0===t&&(t=""),new Promise((function(a,s){
            if(n._socketState===r.OPEN)return console.log("[SocketManager] Already connected"),void a(!0);
        n._authData.token=e,n._authData.playerId=t;
        try{
            console.log("[SocketManager] Connecting to:",n._config.socketUrl),n._socket=new WebSocket(n._config.socketUrl),n._socketState=r.CONNECTING,n._socket.onopen=function(){
            console.log("[SocketManager] WebSocket connected"),n._socketState=r.OPEN,n._reconnectAttempts=0,n._sendLoginRequest(),n._startHeartbeat(),n._processMessageQueue(),a(!0)
    }

    onmessage() {
        n._handleMessage(e)
    }

    onclose() {
        console.log("[SocketManager] WebSocket closed:",e.code,e.reason),n._socketState=r.CLOSED,n._isAuthenticated=!1,n._stopHeartbeat(),1e3!==e.code&&1001!==e.code&&n._handleReconnection()
    }

    onerror() {
        console.error("[SocketManager] WebSocket error:",e),n._emitError({
            code:-1,message:"WebSocket connection error",type:"connection",timestamp:(new Date).toISOString()
    }

}
