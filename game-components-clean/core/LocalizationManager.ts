/*
 * LocalizationManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('LocalizationManager')
export class LocalizationManager extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        this.initializeTranslations(),this.detectLanguageFromURL(),console.log("[LocalizationManager] Initialized with language: "+this._currentLanguage)
    }

    initializeTranslations() {
        this._translations={
            ko:{
            round_info_template:"{
            time
    }

    detectLanguageFromURL() {
        var a=this;
        try{
            var e=new URLSearchParams(window.location.search).get("lang");
        if(console.log("[LocalizationManager] URL search params: "+window.location.search),console.log("[LocalizationManager] Lang parameter: "+e),e&&this.isValidLanguage(e)){
            var t=this._currentLanguage;
        this._currentLanguage=e,console.log("[LocalizationManager] Language detected from URL: "+this._currentLanguage),t!==this._currentLanguage&&setTimeout((function(){
            s.instance.emit(u.LANGUAGE_CHANGED,{
            oldLanguage:t,newLanguage:a._currentLanguage
    }

    refreshLanguageFromURL() {
        this.detectLanguageFromURL()
    }

    isValidLanguage() {
        return-1!==["ko","en","zh-cn","zh-tw","ja","th","km","ms","id","vi"].indexOf(a)
    }

    getText() {
        var t,n=null==(t=this._translations[this._currentLanguage])?void 0:t[a];
        if(!n){
            console.warn("[LocalizationManager] Translation not found for key: "+a+" in language: "+this._currentLanguage);
        var o=null==(r=this._translations.en)?void 0:r[a];
        return o?this.replaceParams(o,e):a
    }

    replaceParams() {
        if(!e)return a;
        var t=a;
        for(var n in e)if(e.hasOwnProperty(n)){
            var r=e[n];
        t=t.replace(new RegExp("\\{
            "+n+"\\
    }

    padZero() {
        void 0===e&&(e=2);
        for(var t=a.toString();
        t.length<e;
        )t="0"+t;
        return t
    }

    formatDate() {
        var e=a.getUTCFullYear(),t=a.getUTCMonth()+1,n=a.getUTCDate();
        switch(this._currentLanguage){
            case"ko":return e+"년 "+t+"월 "+n+"일";
        case"en":return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t-1]+" "+n+", "+e;
        case"zh-cn":case"zh-tw":case"ja":return e+"年"+this.padZero(t)+"月"+this.padZero(n)+"日";
        case"th":return n+" "+["ม.ค.","ก.พ.","มี.ค.","เม.ย.","พ.ค.","มิ.ย.","ก.ค.","ส.ค.","ก.ย.","ต.ค.","พ.ย.","ธ.ค."][t-1]+" "+e;
        case"km":return n+" "+["មករា","កុម្ភៈ","មីនា","មេសា","ឧសភា","មិថុនា","កក្កដា","សីហា","កញ្ញា","តុលា","វិច្ឆិកា","ធ្នូ"][t-1]+" "+e;
        case"ms":return n+" "+["Jan","Feb","Mac","Apr","Mei","Jun","Jul","Ogos","Sep","Okt","Nov","Dis"][t-1]+" "+e;
        case"id":return n+" "+["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][t-1]+" "+e;
        case"vi":return"Ngày "+this.padZero(n)+"/"+this.padZero(t)+"/"+e;
        default:return e+"-"+this.padZero(t)+"-"+this.padZero(n)
    }

    getCurrentLanguage() {
        return this._currentLanguage
    }

    setLanguage() {
        if(this.isValidLanguage(a)){
            var e=this._currentLanguage;
        this._currentLanguage=a,console.log("[LocalizationManager] Language changed from "+e+" to: "+this._currentLanguage),s.instance.emit(u.LANGUAGE_CHANGED,{
            oldLanguage:e,newLanguage:this._currentLanguage
    }

    getRoundInfoText() {
        return this.getText("round_info_template",{
            time:a.toString(),round:e.toString()
    }

    getRoundInfoStatic() {
        return this.getText("round_info_static")
    }

    getStartLabel() {
        return this.getText("start_label")
    }

    getSecondsLabel() {
        return this.getText("seconds_label")
    }

    return n.instance() {
        return n.instance
    }

}
