/*
 * StartPopupView - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('StartPopupView')
export class StartPopupView extends Component {

    // Extracted methods (may need manual cleanup)
    init() {
        this.flowNode.active=!1,this.startNode.active=!1,this.characterNode.active=!1,this.characterNode.setPosition(400,-69,0)
    }

    start() {
        this.init(),this.showCharacter()
    }

    onDestroy() {
        c(this.characterNode).stop(),c(this.flowNode).stop(),c(this.startNode).stop(),this.characterNode.active=!1,this.flowNode.active=!1,this.startNode.active=!1,p.instance.emit(h.STARTUP_POPUP_HIDDEN),this.node.destroy()
    }

    show<PERSON>haracter() {
        var t=this;
        this.characterNode.active=!0,c(this.characterNode).to(1,{
            position:new l(165,-69,0)
    }

}
