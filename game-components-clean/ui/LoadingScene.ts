/*
 * LoadingScene - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('LoadingScene')
export class LoadingScene extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        h.addPersistRootNode(this.node),this.isPersistentRoot=!0
    }

    start() {
        this.initializeProgressBar(),this.startResourceLoading(),console.log("[LoadingScene] Starting resource loading...")
    }

    CompleteLoading() {
        this.isLoadingComplete||(this.isLoadingComplete=!0),this.waitForGameStatus(),this.onGameStatusReceived()
    }

    ResetLoading() {
        this.hasError=!1,this.isLoadingComplete=!1,this.hasSceneLoadStarted=!1,this.hasReceivedGameStatus=!1,this.isWaitingForGameStatus=!1,this.isFinalized=!1,this.detachGameStatusListener(),this.progressBar.progress=0,this.updateStatusMessage("Initializing..."),this.startResourceLoading()
    }

    initializeProgressBar() {
        this.progressBar?(this.progressBar.progress=0,console.log("[LoadingScene] Progress bar initialized to 0%")):console.warn("[LoadingScene] Progress bar not assigned in editor")
    }

    startResourceLoading() {
        var e=i(r().mark((function e(){
            return r().wrap((function(e){
            for(;
        ;
        )switch(e.prev=e.next){
            case 0:return e.prev=0,this.updateProgress(.1,"Initializing..."),e.next=4,this.delay(200);
        case 4:return e.next=6,this.loadGameAssets();
        case 6:return this.updateProgress(.4,"Loading assets..."),e.next=9,this.delay(300);
        case 9:return e.next=11,this.preloadMainScene();
        case 11:return this.updateProgress(.7,"Loading scenes..."),e.next=14,this.delay(200);
        case 14:return e.next=16,this.initializeGameSystems();
        case 16:return this.updateProgress(.9,"Preparing game..."),e.next=19,this.delay(200);
        case 19:this.onResourcesReady(),e.next=26;
        break;
        case 22:e.prev=22,e.t0=e.catch(0),console.error("[LoadingScene] Loading failed:",e.t0),this.handleError("Loading failed");
        case 26:case"end":return e.stop()
    }

}
