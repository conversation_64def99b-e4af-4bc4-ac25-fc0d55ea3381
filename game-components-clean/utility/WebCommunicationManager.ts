/*
 * WebCommunicationManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('WebCommunicationManager')
export class WebCommunicationManager extends Component {

    // Extracted methods (may need manual cleanup)
    start() {
        console.log("[WebCommunicationManager] Initialized for iframe communication"),console.log("[WebCommunicationManager] Environment info:",this.getEnvironmentInfo())
    }

    sendCode3ResultScreen() {
        var n=(new Date).toISOString();
        this.sendPostMessage(3,{
    }

    sendCode4PanelState() {
        var n=(new Date).toISOString();
        this.sendPostMessage(4,{
    }

    sendPostMessage() {
        try{
            if("undefined"!=typeof window&&window.parent&&window.parent!==window){
            var t={
            code:n
    }

    isInIframeContext() {
        try{
            return"undefined"!=typeof window&&window.parent&&window.parent!==window
    }

    getEnvironmentInfo() {
        try{
            return{
            hasWindow:"undefined"!=typeof window,hasParent:"undefined"!=typeof window&&!!window.parent,isIframe:this.isInIframeContext(),userAgent:"undefined"!=typeof navigator?navigator.userAgent:"unknown",timestamp:(new Date).toISOString()
    }

    onDestroy() {
        console.log("[WebCommunicationManager] Destroyed")
    }

    return t.instance() {
        return t.instance
    }

}
