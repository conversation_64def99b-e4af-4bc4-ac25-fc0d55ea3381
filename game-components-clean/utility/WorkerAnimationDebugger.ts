/*
 * WorkerAnimationDebugger - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('WorkerAnimationDebugger')
export class WorkerAnimationDebugger extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        this.towerController||(this.towerController=this.node.getComponentInChildren(c)||this.node.getComponentInParent(c))
    }

    start() {
        this.runDiagnostics()
    }

    runDiagnostics() {
        console.log("🔍 === WORKER ANIMATION DIAGNOSTICS ==="),this.towerController?(console.log("✅ TowerAnimationController found"),this.checkBasicSetup(),this.checkWorkerComponents(),this.testBasicFunctionality(),console.log("🔍 === END DIAGNOSTICS ===")):console.error("❌ TowerAnimationController not found! Please assign it in the inspector.")
    }

    checkBasicSetup() {
        console.log("\n📋 Checking basic setup...");
        var o=this.towerController.getRaceState();
        console.log("Race state:",o);
        var e=this.towerController.getAllTowerStates();
        console.log("Tower states:",e)
    }

    checkWorkerComponents() {
        console.log("\n🔧 Checking WorkersAnimation components..."),this.towerController.debugAllWorkers()
    }

    testBasicFunctionality() {
        var o=this;
        console.log("\n🧪 Testing basic functionality..."),console.log("Testing force show worker1..."),this.towerController.forceShowAllWorker1(),setTimeout((function(){
            console.log("Testing height-based reveals..."),o.towerController.testAllTowerHeights()
    }

    testWorkerVisibility() {
        console.log("🎮 Manual worker visibility test"),this.towerController.forceShowAllWorker1()
    }

    testHeightSystem() {
        console.log("🎮 Manual height system test"),this.towerController.testAllTowerHeights()
    }

    testRacing() {
        console.log("🎮 Manual racing test"),this.towerController.testRacing()
    }

    showDebugInfo() {
        console.log("🎮 Debug info"),this.towerController.debugAllWorkers()
    }

    return null() {
        return null
    }

}
