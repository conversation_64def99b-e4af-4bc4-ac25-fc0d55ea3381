/*
 * TowerAnimationController - Controls multiple tower animations
 * Human-readable version with proper structure
 */

import { _decorator, Component, Node } from 'cc';
import { TowerAnimation } from './TowerAnimation';

const { ccclass, property } = _decorator;

@ccclass('TowerAnimationController')
export class TowerAnimationController extends Component {
    @property(Node)
    towerA: Node = null;

    @property(Node)
    towerP: Node = null;

    @property(Node)
    towerT: Node = null;

    // Array to hold all tower animation components
    private towerAnimations: TowerAnimation[] = [];

    /**
     * Initialize tower animations for all players
     */
    initializeTowerAnimations() {
        // Array of tower nodes for each player
        const towerNodes = [this.towerA, this.towerP, this.towerT];
        const playerNames = ["A", "P", "T"];
        
        // Clear existing animations
        this.towerAnimations = [];
        
        for (let index = 0; index < towerNodes.length; index++) {
            const towerNode = towerNodes[index];
            
            if (towerNode) {
                // Get the TowerAnimation component from the node
                const towerAnimationComponent = towerNode.getComponent(TowerAnimation);
                
                if (towerAnimationComponent) {
                    // Set the player identifier
                    towerAnimationComponent.towerPlayer = playerNames[index];
                    
                    // Add to the animations array for management
                    this.towerAnimations.push(towerAnimationComponent);
                    
                    console.log(`[TowerController] Initialized tower animation for player ${playerNames[index]}`);
                } else {
                    console.warn(`[TowerController] No TowerAnimation component found on tower ${playerNames[index]}`);
                }
            } else {
                console.warn(`[TowerController] Tower node ${playerNames[index]} is null`);
            }
        }
        
        console.log(`[TowerController] Initialized ${this.towerAnimations.length} tower animations`);
    }

    /**
     * Start race for all towers
     */
    startRace(duration: number = 5.0) {
        console.log(`[TowerController] Starting race with duration ${duration}s`);
        
        for (const towerAnimation of this.towerAnimations) {
            towerAnimation.animateToStartPosition(duration);
        }
    }

    /**
     * Reset all towers to initial position
     */
    resetAllTowers() {
        console.log('[TowerController] Resetting all towers');
        
        for (const towerAnimation of this.towerAnimations) {
            towerAnimation.resetPosition();
        }
    }

    /**
     * Get tower animation by player
     */
    getTowerAnimation(player: string): TowerAnimation | null {
        return this.towerAnimations.find(anim => anim.towerPlayer === player) || null;
    }

    /**
     * Get all tower animations
     */
    getAllTowerAnimations(): TowerAnimation[] {
        return [...this.towerAnimations];
    }

    start() {
        this.initializeTowerAnimations();
    }
}