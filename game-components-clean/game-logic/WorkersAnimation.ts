/*
 * WorkersAnimation - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('WorkersAnimation')
export class WorkersAnimation extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        this.initializeWorkers(),this.initializeWorkerStates(),this.setupEventListeners(),this._isInitialized=!0
    }

    onDestroy() {
        this.cleanup()
    }

    initializeWorkers() {
        try{
            this.captureOriginalWorkerPositions(),this.showAndStartWorker(this.worker1Animation,"worker1")||this.setWorkerToRestPose(this.worker1Animation,"worker1"),this.hideWorker(this.worker2Animation),this.hideWorker(this.worker3Animation),this.hideWorker(this.dirtAnimation),this.enableWorkerMovement&&this.initializeMovementState(),this.restoreAllWorkersToOriginalPositions(),this.forceAllAnimationsToPingPong()
    }

    initializeWorkerStates() {
        var e,i,t=f.START_Y,n=f.WORKER_INTEGRATION.HEIGHT_THRESHOLDS.WORKER2_HEIGHT,o=f.WORKER_INTEGRATION.HEIGHT_THRESHOLDS.WORKER3_HEIGHT,r=f.WORKER_INTEGRATION.HEIGHT_THRESHOLDS.DIRT_HEIGHT,a=Number.isFinite(t)?t:0,s=Number.isFinite(n)?n:a,l=Number.isFinite(o)?o:s,h=Number.isFinite(r)?r:l,m=null!=(e=null==(i=this.worker1Animation)||null==(i=i.node)?void 0:i.active)&&e,d=this._activeWorkers.has(this.worker1Animation),c=m||this._currentHeight>=a;
        this._workerStates.set("worker1",{
            id:"worker1",animation:this.worker1Animation,movement:null,heightThreshold:a,isVisible:m,isActive:d,shouldBeVisible:c
    }

    setupEventListeners() {
        k.instance.on(v.TOWER_ALL_RACES_COMPLETE,this.onAllRacesComplete,this),k.instance.on(v.TOWER_INDIVIDUAL_COMPLETE,this.onTowerIndividualComplete,this),k.instance.on(v.GAME_RESET_UI,this.onGameResetUI,this)
    }

    cleanup() {
        k.instance.off(v.TOWER_ALL_RACES_COMPLETE,this.onAllRacesComplete,this),k.instance.off(v.TOWER_INDIVIDUAL_COMPLETE,this.onTowerIndividualComplete,this),k.instance.off(v.GAME_RESULT,this.onRaceResult,this),k.instance.off(v.GAME_RESET_UI,this.onGameResetUI,this),this.stopAllWorkers(),this.stopAllMovement()
    }

    startRacingMode() {
        if(this._isInitialized){
            this._currentMode;
        this.stopAllWorkers(),this._currentMode="racing",this.restoreAllWorkersToOriginalPositions(["worker1"]),this.resetWorkerMovementState("worker1"),this._hasCompletedRace=!1,this._allRacesCompleteProcessed=!1,this._shouldStopWorkers=!1,this._lastGameStatus=-1,this._winLoseAnimationTriggered=!1,this._inWinLoseMode=!1,this.showAndStartWorker(this.worker1Animation,"worker1"),this.ensureMinimumWorkerVisible("racing mode"),this.startAllWorkerMovement()
    }

    startJiggleMode() {
        if(this._isInitialized){
            this._currentMode;
        this.stopAllWorkers(),this._currentMode="jiggle",this.restoreAllWorkersToOriginalPositions(),this.showAndStartWorker(this.worker1Animation,"worker1"),this.ensureMinimumWorkerVisible("jiggle mode"),this.startAllWorkerMovement()
    }

    startIdleMode() {
        this._isInitialized&&"idle"!==this._currentMode&&(this._currentMode="idle",this.stopAllWorkers(),this.restoreAllWorkersToOriginalPositions(),f.WORKER_INTEGRATION.USE_HEIGHT_BASED_REVEALS&&this.startIdleModeWithHeightLogic())
    }

}
