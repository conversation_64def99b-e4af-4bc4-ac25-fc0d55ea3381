/*
 * DynamicRandomPhaseConfig - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('DynamicRandomPhaseConfig')
export class DynamicRandomPhaseConfig extends Component {

    // Extracted methods (may need manual cleanup)
    for(var n,a=[],i=o.START_Y,E=t(e);!(n=E()).done;){var A=n.value;i+=o.TOTAL_DISTANCE*A.distancePercent,a.push(i)() {
        for(var n,a=[],i=o.START_Y,E=t(e);
        !(n=E()).done;
        ){
            var A=n.value;
        i+=o.TOTAL_DISTANCE*A.distancePercent,a.push(i)
    }

    var a,i,E=R(),A=_(),r=A.minDuration,s=A.maxDuration,S=1;if(void 0!==(null==n?void 0:n.totalDuration))i=Math.min(Math.max(n.totalDuration,r),s),a=i,console.log("[DynamicRace] Position "+e+" duration override applied: "+(i/1e3).toFixed(2)+"s");else if(a=I(),S=c(e),(i=a*S)<r?(console.log("[DynamicRace] Position "+e+" total duration "+(i/1e3).toFixed(2)+"s clamped to min "+(r/1e3).toFixed(2)+"s"),i=r):i>s&&(console.log("[DynamicRace] Position "+e+" total duration "+(i/1e3).toFixed(2)+"s clamped to max "+(s/1e3).toFixed(2)+"s"),i=s),i===r&&e>1){var u=120*(e-1);i=Math.min(r+u,s)() {
        var a,i,E=R(),A=_(),r=A.minDuration,s=A.maxDuration,S=1;
        if(void 0!==(null==n?void 0:n.totalDuration))i=Math.min(Math.max(n.totalDuration,r),s),a=i,console.log("[DynamicRace] Position "+e+" duration override applied: "+(i/1e3).toFixed(2)+"s");
        else if(a=I(),S=c(e),(i=a*S)<r?(console.log("[DynamicRace] Position "+e+" total duration "+(i/1e3).toFixed(2)+"s clamped to min "+(r/1e3).toFixed(2)+"s"),i=r):i>s&&(console.log("[DynamicRace] Position "+e+" total duration "+(i/1e3).toFixed(2)+"s clamped to max "+(s/1e3).toFixed(2)+"s"),i=s),i===r&&e>1){
            var u=120*(e-1);
        i=Math.min(r+u,s)
    }

}
