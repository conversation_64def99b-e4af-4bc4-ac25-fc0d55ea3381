/*
 * TowerAnimation - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('TowerAnimation')
export class TowerAnimation extends Component {

    // Extracted methods (may need manual cleanup)
    debugLog() {
        g.PERFORMANCE.ENABLE_DEBUG_LOGGING&&(e=console).log.apply(e,arguments)
    }

    onLoad() {
        this.debugLog("[TowerAnimation] "+this.towerPlayer+" onLoad() called - FORCING timing configuration"),T(8,10),this.towerNode&&this.resetPosition()
    }

    resetPosition() {
        this.towerNode&&(this.cancelCountdownTween(),this.currentY=g.RESET_Y,this.towerNode.setPosition(this.towerNode.position.x,this.currentY,this.towerNode.position.z),this.currentPhase="phase1",this.isRacing=!1,this.wobbleBaseX=this.towerNode.position.x,this.wobbleOffset=0,this.wobbleTargetOffset=0,this.wobbleTimer=0,this.wobbleDirection=-1,this.wobbleCurrentAmplitude=this.wobbleAmplitude,this.wobbleCurrentInterval=Math.max(.005,this.wobbleInterval),this.wobbleSmoothedSpeed=0,this.wobbleSampledY=this.currentY,this.applyTowerWobble(!0),this.debugLog("[TowerAnimation] "+this.towerPlayer+" reset to Y="+this.currentY))
    }

    cancelCountdownTween() {
        this.countdownTween&&(this.countdownTween.stop(),this.countdownTween=null)
    }

    animateToStartPosition() {
        var i=this;
        if(this.towerNode&&!this.isRacing){
            var o=this.resolveCountdownTweenDuration(e,t);
        if(0!==o){
            var n=g.START_Y,r=this.towerNode.getPosition();
        Math.abs(r.y-n)<=.01?this.currentY=n:(this.cancelCountdownTween(),this.countdownTween=u(this.towerNode).to(o,{
            position:new c(r.x,n,r.z)
    }

    i.currentY=i.towerNode.position.y,i.wobbleSampledY=i.currentY() {
        i.currentY=i.towerNode.position.y,i.wobbleSampledY=i.currentY
    }

}
