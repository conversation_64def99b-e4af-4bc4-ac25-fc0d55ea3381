/*
 * FakeLoadingView - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('FakeLoadingView')
export class FakeLoadingView extends Component {

    // Extracted methods (may need manual cleanup)
    start() {
        this.initializeProgressBar(),this.startResourceLoading()
    }

    handleFirstGameStatusChanged() {
        console.log("[LoadingScene] GAME_STATUS_CHANGED received:",null!=(t=null==e?void 0:e.status)?t:"unknown"),this.node.destroy()
    }

    initializeProgressBar() {
        this.progressBar?(this.progressBar.progress=0,console.log("[LoadingScene] Progress bar initialized to 0%")):console.warn("[LoadingScene] Progress bar not assigned in editor")
    }

    startResourceLoading() {
        var e=i(n().mark((function e(){
            return n().wrap((function(e){
            for(;
        ;
        )switch(e.prev=e.next){
            case 0:return e.prev=0,this.updateProgress(.1,"Initializing..."),e.next=4,this.delay(200);
        case 4:return this.updateProgress(.4,"Loading assets..."),e.next=7,this.delay(300);
        case 7:return this.updateProgress(.7,"Loading scenes..."),e.next=10,this.delay(200);
        case 10:return this.updateProgress(.9,"Preparing game..."),e.next=13,this.delay(200);
        case 13:this.onResourcesReady(),e.next=19;
        break;
        case 16:e.prev=16,e.t0=e.catch(0),console.error("[LoadingScene] Loading failed:",e.t0);
        case 19:case"end":return e.stop()
    }

}
