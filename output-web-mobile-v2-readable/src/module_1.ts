/*
 * module_1 - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('module_1')
export class module_1 extends Component {

    // Original minified code (needs manual deobfuscation):
    /*
    /*
 * Auto-generated inline module: module_1
 * Reverse engineered by cc-reverse
 *\/

var s=new CSSStyleSheet();s.replaceSync("+JSON.stringify(e)+');e("default",s)}}})
    */
    
    start() {
        // Component initialization
    }
    
    update(deltaTime: number) {
        // Component update logic
    }
}
