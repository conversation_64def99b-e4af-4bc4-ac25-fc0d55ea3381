/*
 * GameEvents - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('GameEvents')
export class GameEvents extends Component {

    // Extracted methods (may need manual cleanup)
    var n=e[0],o=e[1],E=e[2];switch(r){case t.A:return n;case t.P:return o;case t.T:return E;default:throw new Error("Unknown player: "+r)() {
        var n=e[0],o=e[1],E=e[2];
        switch(r){
            case t.A:return n;
        case t.P:return o;
        case t.T:return E;
        default:throw new Error("Unknown player: "+r)
    }

    return o(e,1)() {
        return o(e,1)
    }

}
