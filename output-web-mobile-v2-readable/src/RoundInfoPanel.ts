/*
 * RoundInfoPanel - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('RoundInfoPanel')
export class RoundInfoPanel extends Component {

    // Extracted methods (may need manual cleanup)
    start() {
        this._eventManager=p.instance,this._localizationManager=_.instance,this.privateSetupEventListeners(),this.privateInitializeLocalization(),this.privateInitializeDisplay(),this.privateStartUpdateTimer(),this.seconsLabel.string=this._localizationManager.getSecondsLabel(),this.round_info_label.string=this._localizationManager.getRoundInfoStatic()
    }

    TestLanguageChange() {
        this._localizationManager&&this._localizationManager.setLanguage(e)
    }

    TestRefreshLanguage() {
        this._localizationManager&&this._localizationManager.refreshLanguageFromURL()
    }

    TestServerTime() {
        this.privateCalculateServerTime();
        this.privateUpdateDateDisplay()
    }

    DebugBuildIssue() {
        DebugBuildIssue
    }

    privateSetupEventListeners() {
        this._eventManager.on(v.GAME_STATUS_CHANGED,this.privateOnGameStatusChanged,this),this._eventManager.on(v.LANGUAGE_CHANGED,this.privateOnLanguageChanged,this),this._eventManager.on(v.RESULT_POPUP_HIDDEN,this.resultHiddenAlready,this),this._eventManager.on(v.GAME_RESET_UI,this.privateOnGameResetUI,this)
    }

    resultHiddenAlready() {
        this.hidePanelEvent=!0,this.privateAnimateToPosition(0,!0)
    }

    privateInitializeLocalization() {
        this._localizationManager
    }

    privateInitializeDisplay() {
        this.statePanelInfo&&(this.statePanelInfo.active=!0);
        var e=this.node.position.clone();
        this.node.setPosition(-500,e.y,e.z)
    }

    privateStartUpdateTimer() {
        this.schedule(this.privateUpdateDateDisplayIfServerTime,1),this.schedule(this.privateUpdateLocalCountdown,1)
    }

    privateUpdateDateDisplayIfServerTime() {
        this._serverTime&&this._lastServerTimeUpdate>0&&this.privateUpdateDateDisplay()
    }

    privateUpdateLocalCountdown() {
        (this._currentStatus===d.BETTING_OPEN||this._currentStatus===d.BETTING_CLOSED)&&this._localTimeRemains>0&&(this._localTimeRemains--,this._localTimeRemains<=0&&this.hidePanel(),this.privateUpdateTimeToNextRound(this._localTimeRemains))
    }

    hidePanel() {
        var e=this;
        this.hidePanelEvent&&(this.hidePanelEvent=!1,this.hasTriggeredCountdownRaise=!1,setTimeout((function(){
            e.privateAnimateToPosition(-500,!1),p.instance.emit(v.STARTUP_POPUP_SHOWN)
    }

    privateOnLanguageChanged() {
        this.privateInitializeLocalization(),this._serverTime&&this._lastServerTimeUpdate>0&&this.privateUpdateDateDisplay()
    }

    privateOnGameStatusChanged() {
        if(e&&"object"==typeof e){
            if(void 0!==e.status&&(this._currentStatus=e.status),void 0!==e.round&&(this._currentRound=e.round),void 0!==e.timeRemains){
            this._timeRemains=e.timeRemains;
        var t=e.timeRemains;
        if(this._currentStatus===d.BETTING_OPEN||this._currentStatus===d.BETTING_CLOSED){
            if(this._currentStatus===d.BETTING_OPEN){
            var i=Math.abs(this._localTimeRemains-t);
        (this._localTimeRemains<0||i>2)&&(this._localTimeRemains=t,this._lastServerSync=Date.now())
    }

    checkFirstShow() {
        this.statePanelInfo&&this.firstShow&&(this.firstShow=!1,this.privateAnimateToPosition(0,!0))
    }

    privateAnimateToPosition() {
        var i=this;
        this._positionTween&&(this._positionTween.stop(),this._positionTween=null),this._isAnimating=!0;
        var n=this.node.position.clone(),a=new h(e,n.y,n.z);
        t||(this.hasTriggeredCountdownRaise=!1);
        var s=m.instance;
        s&&0==e?s.sendCode4PanelState():console.warn("[RoundInfoPanel] WebCommunicationManager not available when showing panel"),this._positionTween=u(this.node).to(this.ANIMATION_DURATION,{
            position:a
    }

}
