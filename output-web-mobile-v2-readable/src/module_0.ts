/*
 * module_0 - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('module_0')
export class module_0 extends Component {

    // Original minified code (needs manual deobfuscation):
    /*
    /*
 * Auto-generated inline module: module_0
 * Reverse engineered by cc-reverse
 *\/

e("default",'+JSON.stringify(e)+")}}})
    */
    
    start() {
        // Component initialization
    }
    
    update(deltaTime: number) {
        // Component update logic
    }
}
