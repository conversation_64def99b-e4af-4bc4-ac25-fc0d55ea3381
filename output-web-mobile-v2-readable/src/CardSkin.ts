/*
 * Card<PERSON>kin - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('CardSkin')
export class CardSkin extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        !this.cardSprite&&this.getComponent(s)&&(this.cardSprite=this.getComponent(s))
    }

    SetCard() {
        if(a&&this.useSprite&&this.cardSprite){
            this.currentCard=a,this.isCardBack=!1;
        var e=this.privateGetCardSpriteFrame(a);
        e?(this.cardSprite.spriteFrame=e,console.log("[CardSkin] Set card: "+a.display)):l("[CardSkin] Sprite frame not found for card: "+a.display)
    }

    SetCardByName() {
        var i=this.privateCreateGameCard(a,e);
        i?this.SetCard(i):l("[<PERSON><PERSON>kin] Invalid card name: "+a+"_"+e)
    }

    SetCardByIndex() {
        if(a<0||a>51)l("[CardSkin] Card index out of range (0-51)");
        else{
            var e=Math.floor(a/13),i=a%13;
        this.SetCardByName(["hearts","diamonds","clubs","spades"][e],["2","3","4","5","6","7","8","9","T","J","Q","K","A"][i])
    }

    ShowCardBack() {
        if(void 0===a&&(a="normal"),this.useSprite&&this.cardSprite){
            this.isCardBack=!0,this.currentCard=null;
        var t,n=null;
        switch(console.log("[CardSkin] "+this.node.name+" showCardBack called with backType: "+a),console.log("[CardSkin] "+this.node.name+" available frames - dragon: "+!!this.cardDragonBack+", tiger: "+!!this.cardTigerBack+", normal: "+!!this.cardBack),a){
            case"dragon":n=this.cardDragonBack,console.log("[CardSkin] "+this.node.name+" selected dragon back frame: "+((null==(e=n)?void 0:e.name)||"null"));
        break;
        case"tiger":n=this.cardTigerBack,console.log("[CardSkin] "+this.node.name+" selected tiger back frame: "+((null==(i=n)?void 0:i.name)||"null"));
        break;
        default:n=this.cardBack,console.log("[CardSkin] "+this.node.name+" selected normal back frame: "+((null==(r=n)?void 0:r.name)||"null"))
    }

    privateGetCardSpriteFrame() {
        var e=[];
        switch(a.suit){
            case"hearts":e=this.heartCards;
        break;
        case"diamonds":e=this.diamondCards;
        break;
        case"clubs":e=this.clubCards;
        break;
        case"spades":e=this.spadeCards;
        break;
        default:return null
    }

    privateGetCardIndex() {
        var e={
            2:0,3:1,4:2,5:3,6:4,7:5,8:6,9:7,10:8,T:8,11:9,J:9,12:10,Q:10,13:11,K:11,a:12,A:12,1:12
    }

    privateCreateGameCard() {
        var i={
            heart:"hearts",hearts:"hearts",dia:"diamonds",diamond:"diamonds",diamonds:"diamonds",clover:"clubs",club:"clubs",clubs:"clubs",spade:"spades",spades:"spades"
    }

    RandomCard() {
        var a=Math.floor(52*Math.random());
        this.SetCardByIndex(a)
    }

    IsShowingBack() {
        return this.isCardBack
    }

    GetCurrentCard() {
        return this.currentCard
    }

    GetCardCount() {
        return 52
    }

    privatePerformFlip() {
        if(void 0===e&&(e=this.defaultFlipDuration),void 0===r&&(r=!1),!this.cardSprite||!this.enableAnimation)return this.cardSprite.spriteFrame=a,this.isCardBack=r,void(i&&i());
        this.StopFlip(),this.isAnimating=!0;
        var t=this.cardSprite.node;
        this.performSimpleFlip(t,a,e,i,r)
    }

    performSimpleFlip() {
        var n=this;
        void 0===t&&(t=!1);
        var o=a.scale.clone();
        this.currentTween=c(a).to(i/2,{
            scale:"horizontal"===this.flipDirection?new u(0,o.y,o.z):new u(o.x,0,o.z)
    }

}
