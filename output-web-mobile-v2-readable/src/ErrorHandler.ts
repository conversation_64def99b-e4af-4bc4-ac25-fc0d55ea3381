/*
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('ErrorHandler')
export class <PERSON>rror<PERSON>andler extends Component {

    // Extracted methods (may need manual cleanup)
    getInstance() {
        return this._instance
    }

    start() {
        t._instance=this,this.privateSetupErrorPopup(),console.log("[<PERSON><PERSON>r<PERSON>and<PERSON>] Initialized")
    }

    privateSetupErrorPopup() {
        this.errorPopup&&(this.errorPopup.active=!1),this.reconnectButton&&this.reconnectButton.on(c.EventType.TOUCH_END,this.onReconnectClicked,this)
    }

    HandleError() {
        switch(console.error("[ErrorHandler] "+r.type.toUpperCase()+" Error:",r.message),this.addToHistory(r),r.type){
            case"connection":this.handleConnectionError(r);
        break;
        case"authentication":this.handleAuthenticationError(r);
        break;
        case"betting":this.handleBettingError(r);
        break;
        case"game":this.handleGameError(r);
        break;
        case"balance":this.handleBalanceError(r);
        break;
        default:this.handleGenericError(r)
    }

    handleConnectionError() {
        var t,e=this;
        (((t={
    }

    handleAuthenticationError() {
        var t,e=this;
        (((t={
    }

    handleBettingError() {
        var t,e=this;
        (((t={
    }

    handleGameError() {
        var t,e=this;
        (((t={
    }

    handleBalanceError() {
        var t,e=this;
        (((t={
    }

    handleGenericError() {
        this.showError("오류가 발생했습니다: "+r.message,!1)
    }

    showError() {
        var e=this;
        void 0===t&&(t=!1),this.errorMessageLabel&&(this.errorMessageLabel.string=r),this.reconnectButton&&(this.reconnectButton.active=t),this.errorPopup&&(this.errorPopup.active=!0),t||this.scheduleOnce((function(){
            e.HideError()
    }

    HideError() {
        this.errorPopup&&(this.errorPopup.active=!1)
    }

    onReconnectClicked() {
        this.HideError(),this.eventTarget.emit("reconnect-requested")
    }

    redirectToLogin() {
        this.eventTarget.emit("logout-required"),this.scheduleOnce((function(){
            "undefined"!=typeof window&&window.location&&(window.location.href="/minigame/landing/index.php")
    }

    addToHistory() {
        this.errorHistory.unshift(r),this.errorHistory.length>this.maxErrorHistory&&(this.errorHistory=this.errorHistory.slice(0,this.maxErrorHistory))
    }

    GetErrorStats() {
        var r={
            total:this.errorHistory.length,byType:{
    }

}
