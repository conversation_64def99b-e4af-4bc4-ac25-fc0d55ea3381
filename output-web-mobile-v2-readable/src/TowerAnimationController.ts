/*
 * TowerAnimationController - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('TowerAnimationController')
export class TowerAnimationController extends Component {

    // Extracted methods (may need manual cleanup)
    debugLog() {
        T.PERFORMANCE.ENABLE_DEBUG_LOGGING&&(t=console).log.apply(t,arguments)
    }

    onLoad() {
        this.debugLog("[TowerAnimationController] onLoad() called - initializing controller"),this.setupTowerPlayers(),this.setupEventListeners(),this.setupTimingConfiguration(),this.debugLog("[TowerAnimationController] onLoad() completed")
    }

    setupTowerPlayers() {
        this.towerAnimationA&&this.towerAnimationA.setTowerPlayer(g.A),this.towerAnimationP&&this.towerAnimationP.setTowerPlayer(g.P),this.towerAnimationT&&this.towerAnimationT.setTowerPlayer(g.T),this.debugLog("[TowerAnimationController] Tower players configured")
    }

    setupEventListeners() {
        w.instance.on(f.TOWER_INDIVIDUAL_COMPLETE,this.handleTowerComplete,this),w.instance.on(f.STARTUP_POPUP_HIDDEN,this.onStartupPopupHidden,this),w.instance.on(f.GAME_RESET_UI,this.onGameResetUI,this),w.instance.on(f.ROUND_COUNTDOWN_STARTED,this.onRoundCountdownStarted,this),this.debugLog("[TowerAnimationController] Event listeners setup - listening for STARTUP_POPUP_HIDDEN, TOWER_INDIVIDUAL_COMPLETE, GAME_RESET_UI, and ROUND_COUNTDOWN_STARTED")
    }

    setupTimingConfiguration() {
        P(8,10);
        var t=b();
        this.debugLog("[TowerAnimationController] Timing configuration set to 8-10 second race duration"),this.debugLog("[TowerAnimationController] Current timing config:",{
            minDuration:t.minDuration/1e3+"s",maxDuration:t.maxDuration/1e3+"s",baseDuration:t.baseDuration/1e3+"s"
    }

    finalLineAnimationRound() {
        this.firstPlayerOfRound||(this.firstPlayerOfRound=!0,m(this.finalLineUIOpacity).to(.2,{
            opacity:255
    }

}
