/*
 * SoundToggleButton - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SoundToggleButton')
export class SoundToggleButton extends Component {

    // Extracted methods (may need manual cleanup)
    start() {
        if(this.gameManager=h.getInstance(),!this.gameManager)return p.instance.on("game-started",this.onGameManagerReady,this),void console.log("[SoundToggleButton] Waiting for GameManager...");
        this.setupButton(),this.updateIconWhenGameStarted()
    }

    setupButton() {
        this.toggleButton&&(this.toggleButton.node.on(s.EventType.CLICK,this.onToggleClick,this),this.toggleButton.node.on(c.EventType.MOUSE_ENTER,this.onMouseEnter,this),this.toggleButton.node.on(c.EventType.MOUSE_LEAVE,this.onMouseLeave,this))
    }

    onGameManagerReady() {
        this.gameManager=h.getInstance(),this.gameManager&&(this.setupButton(),this.updateIcon(),p.instance.off("game-started",this.onGameManagerReady,this))
    }

    onMouseLeave() {
        this.iconSprite.spriteFrame=this.spriteFrameBackup
    }

    onMouseEnter() {
        this.iconSprite.spriteFrame=this.spriteFrameBackup,this.iconSprite.spriteFrame=this.gameManager.IsBGMEnabled()?this.soundHoverWhenOnIcon:this.soundHoverWhenOffIcon
    }

    onToggleClick() {
        var e=this.gameManager.ToggleBGM();
        console.log("[SoundToggleButton] BGM toggled: "+(e?"ON":"OFF")),this.updateIcon()
    }

    updateIcon() {
        if(this.iconSprite&&this.gameManager){
            var e=this.gameManager.IsBGMEnabled();
        e&&this.soundOnIcon?this.iconSprite.spriteFrame=this.soundOnIcon:!e&&this.soundOffIcon&&(this.iconSprite.spriteFrame=this.soundOffIcon),this.spriteFrameBackup=this.iconSprite.spriteFrame
    }

    updateIconWhenGameStarted() {
        this.node.active=!1,this.updateIcon(),this.node.active=!0
    }

    setBGMEnabled() {
        this.gameManager&&(this.gameManager.IsBGMEnabled()!==e&&(this.gameManager.ToggleBGM(),this.updateIcon()))
    }

    getBGMEnabled() {
        return!!this.gameManager&&this.gameManager.IsBGMEnabled()
    }

    onDestroy() {
        this.toggleButton&&this.toggleButton.node.off(s.EventType.CLICK,this.onToggleClick,this)
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

}
