/*
 * builtin_pipeline - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('builtin_pipeline')
export class builtin_pipeline extends Component {

    // Original minified code (needs manual deobfuscation):
    /*
    /*
 * Auto-generated from builtin_pipeline
 * Reverse engineered by cc-reverse
 *\/

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { builtin_pipeline_types } from './builtin-pipeline-types.ts';

e("getPingPongRenderTarget",G),var w=i.AABB,b=i.Sphere,P=i.intersect,T=r.ClearFlagBit,R=r.Color,M=r.Format,v=r.FormatFeatureBit,E=r.LoadOp,C=r.StoreOp,x=r.TextureType,A=r.Viewport,L=s.scene,D=L.CameraUsage,F=L.CSMLevel,N=L.LightType;function O(e){return!!(e.clearFlag&(T.COLOR|T.STENCIL<<1))}function y(e,a,t,i,r,s){e.shadowFixedArea||e.csmLevel===F.LEVEL_1?(r.left=0,r.top=0,r.width=Math.trunc(a),r.height=Math.trunc(t)):(r.left=Math.trunc(i%2*.5*a),r.top=s>0?Math.trunc(.5*(1-Math.floor(i/2))*t):Math.trunc(.5*Math.floor(i/2)*t),r.width=Math.trunc(.5*a),r.height=Math.trunc(.5*t)),r.left=Math.max(0,r.left),r.top=Math.max(0,r.top),r.width=Math.max(1,r.width),r.height=Math.max(1,r.height)}var Q=e("PipelineConfigs",(function(){this.isWeb=!1,this.isWebGL1=!1,this.isWebGPU=!1,this.isMobile=!1,this.isHDR=!1,this.useFloatOutput=!1,this.toneMappingType=0,this.shadowEnabled=!1,this.shadowMapFormat=M.R32F,this.shadowMapSize=new n(1,1),this.usePlanarShadow=!1,this.screenSpaceSignY=1,this.supportDepthSample=!1,this.mobileMaxSpotLightShadowMaps=1,this.platform=new o(0,0,0,0)
    */
    
    start() {
        // Component initialization
    }
    
    update(deltaTime: number) {
        // Component update logic
    }
}
