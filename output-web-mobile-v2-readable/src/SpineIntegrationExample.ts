/*
 * SpineIntegrationExample - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SpineIntegrationExample')
export class SpineIntegrationExample extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        this.node.on("spine-system-ready",this.onSpineSystemReady,this),this.node.on("spine-error",this.onSpineError,this)
    }

    start() {
        var n=this.getComponent(y);
        n&&n.isInitialized()?this.onSpineSystemReady():m("[SpineIntegrationExample] Waiting for Spine system to initialize...")
    }

    onDestroy() {
        this.node.off("spine-system-ready",this.onSpineSystemReady,this),this.node.off("spine-error",this.onSpineError,this)
    }

    onSpineSystemReady() {
        m("[SpineIntegrationExample] Spine system is ready!"),this._isSystemReady=!0,this.spineComponent&&this.initializeSpineComponent(),this.autoPlay&&this.startAutoPlay()
    }

    initializeSpineComponent() {
        var n=a(s().mark((function n(){
            return s().wrap((function(n){
            for(;
        ;
        )switch(n.prev=n.next){
            case 0:if(this.spineComponent){
            n.next=3;
        break
    }

}
