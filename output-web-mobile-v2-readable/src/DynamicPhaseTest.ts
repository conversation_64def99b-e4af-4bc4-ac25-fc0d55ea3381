/*
 * DynamicPhaseTest - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('DynamicPhaseTest')
export class DynamicPhaseTest extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        var e=this;
        this.autoRunTest&&this.scheduleOnce((function(){
            e.runDynamicPhaseTest()
    }

    runDynamicPhaseTest() {
        console.log("=== DYNAMIC RANDOM PHASE ANIMATION TEST ===");
        var e=m.PERFORMANCE.ENABLE_DEBUG_LOGGING;
        m.PERFORMANCE.ENABLE_DEBUG_LOGGING=this.enableDebugLogging,console.log("\n1. GENERATING DYNAMIC CONFIGURATIONS:");
        var o=g(1),n=g(2),t=g(3);
        A(o,"Tower A"),A(n,"Tower P"),A(t,"Tower T"),console.log("\n2. DYNAMIC SYSTEM FEATURES:"),this.showDynamicSystemFeatures(),this.towerAnimationA&&this.towerAnimationP&&this.towerAnimationT?(console.log("\n3. RUNNING DYNAMIC ANIMATIONS:"),this.runAnimationTest()):console.log("\n3. SKIPPING ANIMATION TEST (No tower references)"),m.PERFORMANCE.ENABLE_DEBUG_LOGGING=e
    }

    showDynamicSystemFeatures() {
        console.log("Dynamic Random Phase System Features:"),console.log("  ✓ Random phases: 3-7 per race"),console.log("  ✓ Small actions: 2-5 per phase"),console.log("  ✓ Delay times: 80-150ms between actions"),console.log("  ✓ Realistic effects: hesitation, bursts, fatigue"),console.log("  ✓ Staggered completion with variation"),console.log("  ✓ Configurable 8-10 second timing range"),console.log("  ✓ Smooth sine-based easing profiles"),console.log("  ✓ Progress-based worker reveals"),console.log("  ✓ Advanced performance optimizations")
    }

    runAnimationTest() {
        var e=a(r().mark((function e(){
            var o,n=this;
        return r().wrap((function(e){
            for(;
        ;
        )switch(e.prev=e.next){
            case 0:return e.prev=0,o=[this.towerAnimationA.startRace(1),this.towerAnimationP.startRace(2),this.towerAnimationT.startRace(3)],console.log("Starting dynamic phase races..."),e.next=5,Promise.all(o);
        case 5:console.log("All dynamic phase races completed!"),this.scheduleOnce((function(){
            n.logSystemInfo()
    }

}
