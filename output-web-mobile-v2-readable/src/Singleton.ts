/*
 * <PERSON>ton - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('Singleton')
export class <PERSON>ton extends Component {

    // Extracted methods (may need manual cleanup)
    onLoad() {
        if(n&&n!==this)return console.warn("[ccsingleton] Multiple instances of "+e.name+" detected. Destroying new instance."),void(this.node&&this.node.isValid&&this.node.destroy());
        i.prototype.onLoad&&i.prototype.onLoad.call(this),n=this,r.addPersistRootNode(this.node)
    }

    onDestroy() {
        i.prototype.onDestroy&&i.prototype.onDestroy.call(this),n===this&&(n=null),r.removePersistRootNode(this.node)
    }

    var n=null,i=function(i){function s(){return i.apply(this,arguments)||this() {
        var n=null,i=function(i){
            function s(){
            return i.apply(this,arguments)||this
    }

    return n() {
        return n
    }

    var i=null,r=function(r){function s(){if(i)return console.warn("[singleton] Multiple instances of "+e.name+" detected. Returning existing instance."),i||n(t);for(var o=arguments.length,s=new Array(o),c=0;c<o;c++)s[c]=arguments[c];return t=r.call.apply(r,[this].concat(s))||this,(i=n(t))||n(t)() {
        var i=null,r=function(r){
            function s(){
            if(i)return console.warn("[singleton] Multiple instances of "+e.name+" detected. Returning existing instance."),i||n(t);
        for(var o=arguments.length,s=new Array(o),c=0;
        c<o;
        c++)s[c]=arguments[c];
        return t=r.call.apply(r,[this].concat(s))||this,(i=n(t))||n(t)
    }

    return i||(i=new s),i() {
        return i||(i=new s),i
    }

}
