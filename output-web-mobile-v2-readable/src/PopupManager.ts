/*
 * PopupManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('PopupManager')
export class PopupManager extends Component {

    // Extracted methods (may need manual cleanup)
    getInstance() {
        return this.instance
    }

    onLoad() {
        e.instance=this,this.registerListeners()
    }

    onDestroy() {
        this.detachListeners(),this.disposeNode(this.currentStartPopup),this.disposeNode(this.currentResultPopup),this.currentStartPopup=null,this.currentResultPopup=null,e.instance=null
    }

    registerListeners() {
        P.instance.on(f.TOWER_RESULT,this.onGameResult,this),P.instance.on(f.SHOW_RESULT_POPUP,this.onShowResultPopup,this),P.instance.on(f.GAME_RESET_UI,this.onGameResetUI,this),P.instance.on(f.STARTUP_POPUP_SHOWN,this.onStartupPopupShown,this)
    }

    detachListeners() {
        P.instance.off(f.TOWER_RESULT,this.onGameResult,this),P.instance.off(f.SHOW_RESULT_POPUP,this.onShowResultPopup,this),P.instance.off(f.GAME_RESET_UI,this.onGameResetUI,this),P.instance.off(f.STARTUP_POPUP_SHOWN,this.onStartupPopupShown,this)
    }

    disposeNode() {
        t&&t.isValid&&t.destroy()
    }

    onGameResult() {
        this.latestGameResult=t
    }

    onGameResetUI() {
        this.currentStartPopup=null,this.currentResultPopup=null,this.latestGameResult=null
    }

    onStartupPopupShown() {
        var t=this;
        this.startPopupPrefab?(this.disposeNode(this.currentStartPopup),setTimeout((function(){
            if(t.startPopupPrefab&&t.isValid){
            var e=l(t.startPopupPrefab);
        (t.popupContainer||t.node).addChild(e),t.currentStartPopup=e
    }

    onShowResultPopup() {
        var t=this.transformServerResultToRaceData(this.latestGameResult);
        t&&this.presentResultPopup(t)
    }

    presentResultPopup() {
        if(this.resultPopupPrefab){
            var e=this.transformRaceDataForRankPopup(t);
        if(e){
            this.disposeNode(this.currentResultPopup);
        var n=l(this.resultPopupPrefab);
        (this.popupContainer||this.node).addChild(n),this.currentResultPopup=n;
        var r=n.getComponent("RankPopupView");
        r&&"function"==typeof r.SetupPopup?r.SetupPopup(e):console.warn("[PopupManager] RankPopupView component not found or not ready")
    }

    transformRaceDataForRankPopup() {
        if(!t||!Array.isArray(t.results)||3!==t.results.length)return console.warn("[PopupManager] Invalid race data for result popup:",t),null;
        for(var e,n={
    }

}
