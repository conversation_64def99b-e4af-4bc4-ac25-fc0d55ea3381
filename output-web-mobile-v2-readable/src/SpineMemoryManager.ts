/*
 * SpineMemoryManager - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SpineMemoryManager')
export class SpineMemoryManager extends Component {

    // Extracted methods (may need manual cleanup)
    getInstance() {
        return this._instance
    }

    onLoad() {
        null===n._instance?(n._instance=this,this.initializeMemoryManager()):this.destroy()
    }

    onDestroy() {
        n._instance===this&&(this.cleanup(),n._instance=null)
    }

    initializeMemoryManager() {
        this._memoryThreshold=1024*this.memoryThresholdMB*1024,this._maxConcurrentAnimations=this.maxConcurrentAnimations,this.enableAutoCleanup&&this.schedule(this.performCleanup,this.cleanupInterval),this.schedule(this.monitorMemoryUsage,5),s("[SpineMemoryManager] Initialized with settings:",{
            maxConcurrentAnimations:this._maxConcurrentAnimations,memoryThreshold:this.memoryThresholdMB+"MB",autoCleanup:this.enableAutoCleanup
    }

    registerSpineComponent() {
        e?(this._activeSpineComponents.add(e),this._activeSpineComponents.size>this._maxConcurrentAnimations&&this.optimizeMemoryUsage(),s("[SpineMemoryManager] Registered spine component. Active count: "+this._activeSpineComponents.size)):m("[SpineMemoryManager] Cannot register null spine component")
    }

    unregisterSpineComponent() {
        this._activeSpineComponents.has(e)&&(this._activeSpineComponents.delete(e),s("[SpineMemoryManager] Unregistered spine component. Active count: "+this._activeSpineComponents.size))
    }

}
