/*
 * builtin_pipeline_settings - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('builtin_pipeline_settings')
export class builtin_pipeline_settings extends Component {

    // Extracted methods (may need manual cleanup)
    getPipelineSettings() {
        return this._settings
    }

    onEnable() {
        h(this._settings),this.getComponent(p).camera.pipelineSettings=this._settings
    }

    onDisable() {
        var t=this.getComponent(p).camera;
        t&&(t.pipelineSettings=null)
    }

    _tryEnableEditorPreview() {
        void 0!==m&&(this._editorPreview?m.setEditorPipelineSettings(this._settings):this._disableEditorPreview())
    }

    _disableEditorPreview() {
        void 0!==m&&(m.getEditorPipelineSettings()===this._settings&&m.setEditorPipelineSettings(null))
    }

    return this._editorPreview() {
        return this._editorPreview
    }

    this._editorPreview=t() {
        this._editorPreview=t
    }

    return this._settings.msaa.enabled() {
        return this._settings.msaa.enabled
    }

    this._settings.msaa.enabled=t() {
        this._settings.msaa.enabled=t
    }

    return this._settings.msaa.sampleCount() {
        return this._settings.msaa.sampleCount
    }

    t=Math.pow(2,Math.ceil(Math.log2(Math.max(t,2)))),t=Math.min(t,4),this._settings.msaa.sampleCount=t() {
        t=Math.pow(2,Math.ceil(Math.log2(Math.max(t,2)))),t=Math.min(t,4),this._settings.msaa.sampleCount=t
    }

    return this._settings.enableShadingScale() {
        return this._settings.enableShadingScale
    }

    this._settings.enableShadingScale=t() {
        this._settings.enableShadingScale=t
    }

    return this._settings.shadingScale() {
        return this._settings.shadingScale
    }

    this._settings.shadingScale=t() {
        this._settings.shadingScale=t
    }

    return this._settings.bloom.enabled() {
        return this._settings.bloom.enabled
    }

    this._settings.bloom.enabled=t() {
        this._settings.bloom.enabled=t
    }

    return this._settings.bloom.type() {
        return this._settings.bloom.type
    }

    this._settings.bloom.type=t() {
        this._settings.bloom.type=t
    }

    return this._settings.bloom.kawaseFilterMaterial() {
        return this._settings.bloom.kawaseFilterMaterial
    }

    this._settings.bloom.kawaseFilterMaterial!==t&&(this._settings.bloom.kawaseFilterMaterial=t)() {
        this._settings.bloom.kawaseFilterMaterial!==t&&(this._settings.bloom.kawaseFilterMaterial=t)
    }

    return this._settings.bloom.mipmapFilterMaterial() {
        return this._settings.bloom.mipmapFilterMaterial
    }

    this._settings.bloom.mipmapFilterMaterial!==t&&(this._settings.bloom.mipmapFilterMaterial=t)() {
        this._settings.bloom.mipmapFilterMaterial!==t&&(this._settings.bloom.mipmapFilterMaterial=t)
    }

    return this._settings.bloom.enableAlphaMask() {
        return this._settings.bloom.enableAlphaMask
    }

    this._settings.bloom.enableAlphaMask=t() {
        this._settings.bloom.enableAlphaMask=t
    }

    return this._settings.bloom.iterations() {
        return this._settings.bloom.iterations
    }

    this._settings.bloom.iterations=t() {
        this._settings.bloom.iterations=t
    }

    return this._settings.bloom.threshold() {
        return this._settings.bloom.threshold
    }

    this._settings.bloom.threshold=t() {
        this._settings.bloom.threshold=t
    }

    return this._settings.bloom.intensity() {
        return this._settings.bloom.intensity
    }

    this._settings.bloom.intensity=t() {
        this._settings.bloom.intensity=t
    }

    return this._settings.colorGrading.enabled() {
        return this._settings.colorGrading.enabled
    }

    this._settings.colorGrading.enabled=t() {
        this._settings.colorGrading.enabled=t
    }

    return this._settings.colorGrading.material() {
        return this._settings.colorGrading.material
    }

    this._settings.colorGrading.material!==t&&(this._settings.colorGrading.material=t)() {
        this._settings.colorGrading.material!==t&&(this._settings.colorGrading.material=t)
    }

    return this._settings.colorGrading.contribute() {
        return this._settings.colorGrading.contribute
    }

    this._settings.colorGrading.contribute=t() {
        this._settings.colorGrading.contribute=t
    }

    return this._settings.colorGrading.colorGradingMap() {
        return this._settings.colorGrading.colorGradingMap
    }

    this._settings.colorGrading.colorGradingMap=t() {
        this._settings.colorGrading.colorGradingMap=t
    }

    return this._settings.fxaa.enabled() {
        return this._settings.fxaa.enabled
    }

    this._settings.fxaa.enabled=t() {
        this._settings.fxaa.enabled=t
    }

    return this._settings.fxaa.material() {
        return this._settings.fxaa.material
    }

    this._settings.fxaa.material!==t&&(this._settings.fxaa.material=t)() {
        this._settings.fxaa.material!==t&&(this._settings.fxaa.material=t)
    }

    return this._settings.fsr.enabled() {
        return this._settings.fsr.enabled
    }

    this._settings.fsr.enabled=t() {
        this._settings.fsr.enabled=t
    }

    return this._settings.fsr.material() {
        return this._settings.fsr.material
    }

    this._settings.fsr.material!==t&&(this._settings.fsr.material=t)() {
        this._settings.fsr.material!==t&&(this._settings.fsr.material=t)
    }

    return this._settings.fsr.sharpness() {
        return this._settings.fsr.sharpness
    }

    this._settings.fsr.sharpness=t() {
        this._settings.fsr.sharpness=t
    }

    return this._settings.toneMapping.material() {
        return this._settings.toneMapping.material
    }

    this._settings.toneMapping.material!==t&&(this._settings.toneMapping.material=t)() {
        this._settings.toneMapping.material!==t&&(this._settings.toneMapping.material=t)
    }

    return f()() {
        return f()
    }

    return!1() {
        return!1
    }

}
