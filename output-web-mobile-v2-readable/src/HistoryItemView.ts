/*
 * HistoryItemView - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('HistoryItemView')
export class HistoryItemView extends Component {

    // Extracted methods (may need manual cleanup)
    Setup() {
        console.log("[HistoryItemView] Setup called with data:",e),this.roundLabel?(this.roundLabel.string=e.round.toString(),console.log("[HistoryItemView] Set round:",e.round)):console.warn("[HistoryItemView] roundLabel not found"),this.resultLabel?(this.resultLabel.string=e.result,console.log("[HistoryItemView] Set result:",e.result,"→",e.result)):console.warn("[HistoryItemView] resultLabel not found"),this.iconSpriteSkin?(this.iconSpriteSkin.setSkin("D"===e.result?0:1),console.log("[HistoryItemView] Set icon skin:","D"===e.result?0:1)):console.warn("[HistoryItemView] iconSpriteSkin not found")
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

    return null() {
        return null
    }

}
