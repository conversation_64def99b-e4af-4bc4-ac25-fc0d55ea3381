/*
 * AuthManager - Authentication and Token Management
 * Deobfuscated from reverse engineered code
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

interface AuthData {
    token: string;
    playerId: string;
    sessionId: string;
    balance: number;
    isAuthenticated: boolean;
    userProfile?: any;
    bettingLimits?: any;
}

interface AuthConfig {
    defaultPlayerId: string;
    sessionTimeout: number;
    defaultToken: string;
}

@ccclass('AuthManager')
export class AuthManager extends Component {
    private static _instance: AuthManager = null;
    
    private _authData: AuthData = {
        token: "",
        playerId: "",
        sessionId: "",
        balance: 0,
        isAuthenticated: false
    };
    
    private _config: AuthConfig = {
        defaultPlayerId: "player_1",
        sessionTimeout: 3600000, // 1 hour
        defaultToken: "GEdR9jerB1iLv3EwwwWaQ94xRygrtRchNOqbqU4Ctq3%2btDYtl9MU%2bS5pYqku8mV41gx4KKEgk4gI7t4iQCjo5gsVvieLH7b7hX%2bCf7lZw%2b0%3d"
    };

    static getInstance(): AuthManager {
        return this._instance;
    }

    start() {
        AuthManager._instance = this;
        console.log("[AuthManager] Initialized");
    }

    extractTokenFromURL(): string {
        try {
            const token = new URLSearchParams(window.location.search).get("token");
            if (token) {
                console.log("[AuthManager] Token extracted from URL");
                return decodeURIComponent(token);
            } else {
                console.log("[AuthManager] No token found in URL parameters");
                return "";
            }
        } catch (error) {
            console.error("[AuthManager] Failed to extract token from URL:", error);
            return "";
        }
    }

    initializeWithToken(token: string, playerId?: string): { success: boolean; error?: string } {
        if (token && token.trim().length !== 0) {
            console.log("[AuthManager] Initializing with provided token");
            return this.initializeAuth(token.trim(), playerId || this._config.defaultPlayerId);
        } else {
            return { success: false, error: "Token parameter is required" };
        }
    }

    validateToken(token: string): { valid: boolean; error?: string } {
        if (!token || token.trim().length === 0) {
            return { valid: false, error: "Token is empty" };
        }
        
        if (token.length < 50 || token.length > 200) {
            return { valid: false, error: "Token length is invalid" };
        }
        
        try {
            if (atob(token).length < 10) {
                return { valid: false, error: "Decoded token is too short" };
            } else {
                console.log("[AuthManager] Token validation successful");
                return { valid: true };
            }
        } catch (error) {
            return { valid: false, error: "Token is not valid base64" };
        }
    }

    initializeAuth(token: string, playerId: string): { success: boolean; error?: string } {
        const validation = this.validateToken(token);
        if (validation.valid) {
            this._authData.token = token;
            this._authData.playerId = playerId || "";
            this._authData.isAuthenticated = false;
            console.log("[AuthManager] Authentication initialized");
            return { success: true };
        } else {
            return { success: false, error: validation.error };
        }
    }

    setAuthenticationStatus(isAuthenticated: boolean, sessionId?: string, balance?: number) {
        this._authData.isAuthenticated = isAuthenticated;
        if (sessionId) {
            this._authData.sessionId = sessionId;
        }
        if (balance !== undefined) {
            this._authData.balance = balance;
        }
        console.log("[AuthManager] Authentication status updated:", isAuthenticated);
    }

    updateBalance(balance: number) {
        this._authData.balance = balance;
        console.log("[AuthManager] Balance updated:", balance);
    }

    getAuthData(): AuthData {
        return { ...this._authData };
    }

    getToken(): string {
        return this._authData.token;
    }

    isAuthenticated(): boolean {
        return this._authData.isAuthenticated && this._authData.token.length > 0;
    }

    getBalance(): number {
        return this._authData.balance;
    }

    clearAuth() {
        this._authData = {
            token: "",
            playerId: "",
            sessionId: "",
            balance: 0,
            isAuthenticated: false
        };
        console.log("[AuthManager] Authentication data cleared");
    }

    getAuthHeaders(): Record<string, string> {
        return {
            "Authorization": "Bearer " + this._authData.token,
            "X-Player-ID": this._authData.playerId,
            "X-Session-ID": this._authData.sessionId
        };
    }

    autoInitialize(): { success: boolean; error?: string } {
        const urlToken = this.extractTokenFromURL();
        if (urlToken) {
            console.log("[AuthManager] Using token from URL");
            return this.initializeAuth(urlToken, this._config.defaultPlayerId);
        }
        
        console.log("[AuthManager] No token found in URL parameters, trying default token");
        if (this._config.defaultToken) {
            console.log("[AuthManager] Using default token");
            const decodedToken = decodeURIComponent(this._config.defaultToken);
            const result = this.initializeAuth(decodedToken, this._config.defaultPlayerId);
            if (result.success) {
                return result;
            } else {
                console.error("[AuthManager] Default token validation failed:", result.error);
                return { success: false, error: "Default token validation failed: " + result.error };
            }
        }
        
        console.log("[AuthManager] No default token configured");
        return { success: false, error: "No token found in URL and no default token configured" };
    }
}