/*
 * SocketManager - WebSocket Communication Manager
 * Manually deobfuscated from reverse engineered code
 */

import { _decorator, Component } from 'cc';
import { EventManager } from './EventManager';
import { AuthManager } from './AuthManager';

const { ccclass, property } = _decorator;

interface SocketConfig {
    url: string;
    reconnectInterval: number;
    maxReconnectAttempts: number;
    heartbeatInterval: number;
}

@ccclass('SocketManager')
export class SocketManager extends Component {
    private static _instance: SocketManager = null;
    
    private socket: WebSocket = null;
    private isConnected: boolean = false;
    private isConnecting: boolean = false;
    private reconnectAttempts: number = 0;
    private heartbeatTimer: number = null;
    private reconnectTimer: number = null;
    
    private config: SocketConfig = {
        url: 'wss://game-server.example.com/ws',
        reconnectInterval: 5000,
        maxReconnectAttempts: 10,
        heartbeatInterval: 30000
    };

    static getInstance(): SocketManager {
        return this._instance;
    }

    start() {
        SocketManager._instance = this;
        console.log("[SocketManager] Initialized");
    }

    /**
     * Connect to WebSocket server
     */
    connect(url?: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            if (this.isConnected || this.isConnecting) {
                resolve(this.isConnected);
                return;
            }

            const socketUrl = url || this.config.url;
            console.log(`[SocketManager] Connecting to ${socketUrl}`);
            
            this.isConnecting = true;
            this.socket = new WebSocket(socketUrl);
            
            this.socket.onopen = () => {
                console.log("[SocketManager] Connected successfully");
                this.isConnected = true;
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.startHeartbeat();
                resolve(true);
            };
            
            this.socket.onmessage = (event) => {
                this.handleMessage(event.data);
            };
            
            this.socket.onclose = () => {
                console.log("[SocketManager] Connection closed");
                this.handleDisconnection();
                resolve(false);
            };
            
            this.socket.onerror = (error) => {
                console.error("[SocketManager] Connection error:", error);
                this.isConnecting = false;
                reject(error);
            };
        });
    }

    /**
     * Disconnect from server
     */
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        this.isConnected = false;
        this.isConnecting = false;
        this.stopHeartbeat();
        this.stopReconnect();
    }

    /**
     * Send message to server
     */
    send(message: any): boolean {
        if (!this.isConnected || !this.socket) {
            console.warn("[SocketManager] Cannot send message - not connected");
            return false;
        }

        try {
            const data = typeof message === 'string' ? message : JSON.stringify(message);
            this.socket.send(data);
            return true;
        } catch (error) {
            console.error("[SocketManager] Failed to send message:", error);
            return false;
        }
    }

    /**
     * Handle incoming messages
     */
    private handleMessage(data: string) {
        try {
            const message = JSON.parse(data);
            console.log("[SocketManager] Received message:", message);
            
            // Route message to appropriate handler
            EventManager.getInstance().emit('socket-message', message);
            
        } catch (error) {
            console.error("[SocketManager] Failed to parse message:", error);
        }
    }

    /**
     * Handle disconnection
     */
    private handleDisconnection() {
        this.isConnected = false;
        this.isConnecting = false;
        this.stopHeartbeat();
        
        EventManager.getInstance().emit('socket-disconnected');
        
        // Attempt reconnection
        if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }

    /**
     * Schedule reconnection attempt
     */
    private scheduleReconnect() {
        this.reconnectAttempts++;
        console.log(`[SocketManager] Scheduling reconnect attempt ${this.reconnectAttempts}`);
        
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, this.config.reconnectInterval);
    }

    /**
     * Start heartbeat
     */
    private startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            this.send({ type: 'ping', timestamp: Date.now() });
        }, this.config.heartbeatInterval);
    }

    /**
     * Stop heartbeat
     */
    private stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    /**
     * Stop reconnection timer
     */
    private stopReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }

    // Getters
    getConnectionStatus(): boolean { return this.isConnected; }
    getReconnectAttempts(): number { return this.reconnectAttempts; }
}