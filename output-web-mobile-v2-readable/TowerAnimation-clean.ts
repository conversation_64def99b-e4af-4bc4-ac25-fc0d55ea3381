/*
 * TowerAnimation - Tower Racing Animation Controller
 * Manually deobfuscated from reverse engineered code
 */

import { _decorator, Component, Node, Vec3, Tween, tween } from 'cc';
import { GameEvents } from './GameEvents';
import { EventManager } from './EventManager';
import { WorkersAnimation } from './WorkersAnimation';
import { DynamicRandomPhaseConfig } from './DynamicRandomPhaseConfig';

const { ccclass, property } = _decorator;

enum TowerPlayer {
    A = 'A',
    P = 'P',
    T = 'T'
}

enum RacePhase {
    PHASE1 = 'phase1',
    PHASE2 = 'phase2',
    PHASE3 = 'phase3'
}

@ccclass('TowerAnimation')
export class TowerAnimation extends Component {
    @property(Node)
    towerNode: Node = null;

    @property(WorkersAnimation)
    workersAnimation: WorkersAnimation = null;

    @property({ tooltip: "Tower player identifier (A, P, T)" })
    towerPlayer: TowerPlayer = TowerPlayer.A;

    @property({ tooltip: "Horizontal jiggle amplitude", range: [0, 10, 0.1], slide: true })
    wobbleAmplitude: number = 2.0;

    @property({ tooltip: "Seconds between jiggle flips", range: [0.01, 0.5, 0.01], slide: true })
    wobbleInterval: number = 0.02;

    @property({ tooltip: "Optional smoothing window", range: [0, 0.5, 0.01], slide: true })
    wobbleSmoothing: number = 0.1;

    @property({ tooltip: "Strength of speed-follow behaviour", range: [0, 3, 0.1], slide: true })
    wobbleSpeedFollowStrength: number = 1.0;

    @property({ tooltip: "Reference vertical speed", range: [5, 80, 1], slide: true })
    wobbleSpeedReference: number = 20.0;

    @property({ tooltip: "Smoothing for tower speed sampling", range: [0, 1, 0.05], slide: true })
    wobbleSpeedSmoothing: number = 0.3;

    // Animation state
    private isRacing: boolean = false;
    private activeTween: Tween<Node> = null;
    private finalPosition: number = 1;
    private raceStartTime: number = 0;
    private currentPhase: RacePhase = RacePhase.PHASE1;
    private currentY: number = 0;

    // Wobble animation state
    private wobbleActive: boolean = false;
    private wobbleBaseX: number = 0;
    private wobbleOffset: number = 0;
    private wobbleTargetOffset: number = 0;
    private wobbleDirection: number = 1;
    private wobbleTimer: number = 0;
    private wobbleCurrentAmplitude: number = 0;
    private wobbleCurrentInterval: number = 0.02;
    private wobbleSmoothedSpeed: number = 0;
    private wobbleSampledY: number = 0;

    // Dynamic race configuration
    private dynamicRaceConfig: DynamicRandomPhaseConfig = null;
    private currentActionIndex: number = 0;
    private raceDurationMultiplier: number = 1;
    private plannedDurationMs: number = null;
    private countdownTween: Tween<Node> = null;

    // Constants (would be imported from a constants file)
    private static readonly RESET_Y = 0;
    private static readonly START_Y = 100;

    debugLog(...args: any[]) {
        // Only log if debug logging is enabled
        if (this.isDebugLoggingEnabled()) {
            console.log(...args);
        }
    }

    onLoad() {
        this.debugLog(`[TowerAnimation] ${this.towerPlayer} onLoad() called - FORCING timing configuration`);
        
        // Force timing configuration
        this.setTimingConfiguration(8, 10);
        
        if (this.towerNode) {
            this.resetPosition();
        }
    }

    /**
     * Reset tower to initial position
     */
    resetPosition() {
        if (!this.towerNode) return;

        this.cancelCountdownTween();
        
        this.currentY = TowerAnimation.RESET_Y;
        this.towerNode.setPosition(
            this.towerNode.position.x,
            this.currentY,
            this.towerNode.position.z
        );
        
        this.currentPhase = RacePhase.PHASE1;
        this.isRacing = false;
        
        // Reset wobble state
        this.wobbleBaseX = this.towerNode.position.x;
        this.wobbleOffset = 0;
        this.wobbleTargetOffset = 0;
        this.wobbleTimer = 0;
        this.wobbleDirection = -1;
        this.wobbleCurrentAmplitude = this.wobbleAmplitude;
        this.wobbleCurrentInterval = Math.max(0.005, this.wobbleInterval);
        this.wobbleSmoothedSpeed = 0;
        this.wobbleSampledY = this.currentY;
        
        this.applyTowerWobble(true);
        
        this.debugLog(`[TowerAnimation] ${this.towerPlayer} reset to Y=${this.currentY}`);
    }

    /**
     * Cancel countdown tween
     */
    cancelCountdownTween() {
        if (this.countdownTween) {
            this.countdownTween.stop();
            this.countdownTween = null;
        }
    }

    /**
     * Animate tower to start position
     */
    animateToStartPosition(duration: number, delay?: number) {
        if (!this.towerNode || this.isRacing) return;

        const tweenDuration = this.resolveCountdownTweenDuration(duration, delay);
        if (tweenDuration === 0) return;

        const targetY = TowerAnimation.START_Y;
        const currentPos = this.towerNode.getPosition();

        if (Math.abs(currentPos.y - targetY) <= 0.01) {
            this.currentY = targetY;
            return;
        }

        this.cancelCountdownTween();
        
        this.countdownTween = tween(this.towerNode)
            .to(tweenDuration, {
                position: new Vec3(currentPos.x, targetY, currentPos.z)
            }, {
                easing: 'sineOut',
                onUpdate: () => {
                    this.currentY = this.towerNode.position.y;
                    this.wobbleSampledY = this.currentY;
                }
            })
            .call(() => {
                this.currentY = targetY;
                this.wobbleSampledY = this.currentY;
                this.countdownTween = null;
            })
            .start();
    }

    /**
     * Apply tower wobble effect
     */
    private applyTowerWobble(force: boolean = false) {
        // Wobble animation implementation
        if (this.towerNode) {
            const newX = this.wobbleBaseX + this.wobbleOffset;
            this.towerNode.setPosition(newX, this.towerNode.position.y, this.towerNode.position.z);
        }
    }

    private resolveCountdownTweenDuration(duration: number, delay?: number): number {
        // Calculate actual tween duration based on parameters
        return duration || 1.0;
    }

    private setTimingConfiguration(param1: number, param2: number) {
        // Set timing configuration for animations
        this.debugLog(`[TowerAnimation] Setting timing configuration: ${param1}, ${param2}`);
    }

    private isDebugLoggingEnabled(): boolean {
        // Check if debug logging is enabled (would check global performance settings)
        return true; // For now, always enable
    }
}