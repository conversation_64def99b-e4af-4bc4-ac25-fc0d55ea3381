{"compilerOptions": {"module": "commonjs", "lib": ["es2015", "es2017", "es2020", "dom"], "target": "es6", "experimentalDecorators": true, "skipLibCheck": true, "outDir": "temp/vscode-dist", "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "strict": true, "noImplicitAny": false, "downlevelIteration": true, "esModuleInterop": true, "isolatedModules": true, "baseUrl": "./", "paths": {"cc": ["./temp/declarations/cc"], "cc/*": ["./temp/declarations/cc/*"]}, "types": ["cc"]}, "exclude": ["node_modules", "library", "local", "temp", "build", "settings"]}