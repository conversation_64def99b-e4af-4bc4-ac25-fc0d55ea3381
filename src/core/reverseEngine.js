/*
 * @Date: 2025-06-07 10:06:12
 * @Description: Cocos Creator 逆向工程核心引擎
 */
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { fileManager } = require('../utils/fileManager');
const { codeAnalyzer } = require('./codeAnalyzer');
const { resourceProcessor } = require('./resourceProcessor');
const { projectGenerator } = require('./projectGenerator');
const { logger } = require('../utils/logger');
const { loadConfig } = require('../config/configLoader');

// 将异步文件操作转为 Promise
const readFile = promisify(fs.readFile);
const readdir = promisify(fs.readdir);
const mkdir = promisify(fs.mkdir);

/**
 * 逆向工程主函数
 * @param {Object} options 配置选项
 * @param {string} options.sourcePath 源项目路径
 * @param {string} options.outputPath 输出路径
 * @param {boolean} options.verbose 是否显示详细日志
 * @param {boolean} options.silent 是否静默模式
 * @param {string} options.versionHint 版本提示
 * @returns {Promise<void>}
 */
async function reverseProject(options) {
  const { sourcePath, outputPath, verbose = false, versionHint } = options;
  
  // 全局配置初始化
  global.config = loadConfig();
  global.verbose = verbose;
  
  // 检测Cocos Creator版本并设置相应的文件路径
  const projectInfo = detectProjectVersion(sourcePath, versionHint);
  global.cocosVersion = projectInfo.version;
  
  // 检查文件是否存在
  validatePaths(projectInfo.resPath, projectInfo.settingsPath, projectInfo.projectPath);
  
  // 创建临时目录和输出目录
  const tempPath = path.resolve(outputPath, 'temp');
  const astPath = path.resolve(tempPath, 'ast');
  await mkdir(tempPath, { recursive: true });
  await mkdir(astPath, { recursive: true });
  await mkdir(outputPath, { recursive: true });
  
  // 保存全局路径信息
  global.paths = {
    source: sourcePath,
    output: outputPath,
    res: projectInfo.resPath,
    temp: tempPath,
    ast: astPath
  };

  // 读取项目文件
  try {
    // 读取和解析设置
    const settings = await readFile(projectInfo.settingsPath);
    parseSettings(settings);

    // 开始处理
    logger.info('开始分析代码...');

    // 处理所有JavaScript文件，包括资源包
    await processAllJavaScriptFiles(sourcePath);

    logger.info('开始处理资源...');
    await resourceProcessor.processResources();

    logger.info('生成项目文件...');
    await projectGenerator.generateProject();

    // 清理临时文件
    if (!verbose) {
      await fileManager.cleanDirectory(tempPath);
    }

    return true;
  } catch (err) {
    logger.error('处理项目文件时出错:', err);
    throw err;
  }
}

/**
 * 处理所有JavaScript文件，包括资源包
 * @param {string} sourcePath 源项目路径
 */
async function processAllJavaScriptFiles(sourcePath) {
  const jsFiles = [];

  // 查找所有JavaScript文件
  await findJavaScriptFiles(sourcePath, jsFiles);

  logger.info(`找到 ${jsFiles.length} 个JavaScript文件`);

  // 按优先级排序文件
  const sortedFiles = prioritizeFiles(jsFiles);

  // 处理每个文件
  for (const filePath of sortedFiles) {
    try {
      const relativePath = path.relative(sourcePath, filePath);
      logger.debug(`处理文件: ${relativePath}`);

      const content = await readFile(filePath);
      const code = content.toString('utf-8');

      // 分析代码
      await codeAnalyzer.analyze(code, relativePath);
    } catch (err) {
      logger.warn(`处理文件 ${filePath} 时出错:`, err.message);
    }
  }
}

/**
 * 递归查找所有JavaScript文件
 * @param {string} dir 目录路径
 * @param {Array} jsFiles 文件列表
 */
async function findJavaScriptFiles(dir, jsFiles) {
  try {
    const entries = await readdir(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      if (entry.isDirectory()) {
        // 递归处理子目录
        await findJavaScriptFiles(fullPath, jsFiles);
      } else if (entry.isFile() && entry.name.endsWith('.js')) {
        jsFiles.push(fullPath);
      }
    }
  } catch (err) {
    // 忽略无法访问的目录
    logger.debug(`无法访问目录 ${dir}:`, err.message);
  }
}

/**
 * 按优先级排序文件
 * @param {Array} files 文件列表
 * @returns {Array} 排序后的文件列表
 */
function prioritizeFiles(files) {
  return files.sort((a, b) => {
    // 优先级：主文件 > 资源包 > 其他文件
    const getPriority = (filePath) => {
      const fileName = path.basename(filePath);
      const dirName = path.dirname(filePath);

      // 主要入口文件
      if (['index.js', 'application.js', 'main.js'].includes(fileName)) {
        return 1;
      }

      // 资源包文件
      if (dirName.includes('assets') || dirName.includes('chunks')) {
        return 2;
      }

      // Cocos引擎文件
      if (dirName.includes('cocos-js') || fileName.includes('cc.js')) {
        return 3;
      }

      // 其他文件
      return 4;
    };

    return getPriority(a) - getPriority(b);
  });
}

/**
 * 检测Cocos Creator项目版本并返回相应的文件路径
 * @param {string} sourcePath 源项目路径
 * @param {string} versionHint 版本提示
 * @returns {Object} 包含版本信息和文件路径的对象
 */
function detectProjectVersion(sourcePath, versionHint) {
  // 3.8.x版本的可能路径
  const paths38x = {
    // 3.8.x 主要检查build目录下的文件，支持TypeScript和新的模块系统
    settings: [
      path.resolve(sourcePath, 'application.js'),
      path.resolve(sourcePath, 'settings.js'),
      path.resolve(sourcePath, 'index.js'),
      path.resolve(sourcePath, 'main.js')
    ],
    project: [
      path.resolve(sourcePath, 'cc.js'),
      path.resolve(sourcePath, 'index.js'),
      path.resolve(sourcePath, 'application.js'),
      path.resolve(sourcePath, 'main.js')
    ],
    res: [
      path.resolve(sourcePath, 'assets'),
      path.resolve(sourcePath, 'res')
    ],
    // 3.8.x特有的标识文件
    indicators: [
      path.resolve(sourcePath, 'cc.js'), // Cocos Creator 3.x 特有的引擎文件
      path.resolve(sourcePath, 'application.js'), // 应用程序入口
      path.resolve(sourcePath, 'index.js') // 主入口文件
    ]
  };

  // 2.4.x版本的可能路径
  const paths24x = {
    // 2.4.x 主要检查build目录下的文件
    settings: [
      path.resolve(sourcePath, 'main.js'),
      path.resolve(sourcePath, 'settings.js'),
      path.resolve(sourcePath, 'src/settings.js')
    ],
    project: [
      path.resolve(sourcePath, 'project.js'),
      path.resolve(sourcePath, 'main.js'),
      path.resolve(sourcePath, 'src/project.js')
    ],
    res: [
      path.resolve(sourcePath, 'assets'),
      path.resolve(sourcePath, 'res'),
      path.resolve(sourcePath, 'src/assets')
    ]
  };

  // 2.3.x及以下版本的路径
  const paths23x = {
    settings: [path.resolve(sourcePath, 'src/settings.js')],
    project: [path.resolve(sourcePath, 'src/project.js')],
    res: [path.resolve(sourcePath, 'res')]
  };

  // 检测文件存在性并确定版本
  function findExistingPath(pathArray) {
    for (const filePath of pathArray) {
      if (fs.existsSync(filePath)) {
        return filePath;
      }
    }
    return null;
  }

  // 检测是否为3.8.x版本的特殊函数
  function detect38xVersion() {
    // 检查是否存在3.8.x特有的标识文件
    const indicators = paths38x.indicators.filter(path => fs.existsSync(path));
    if (indicators.length > 0) {
      // 进一步验证：检查文件内容是否包含3.x特有的标识
      for (const indicator of indicators) {
        try {
          const content = fs.readFileSync(indicator, 'utf-8');
          // 检查是否包含Cocos Creator 3.x的特征
          if (content.includes('System.import') ||
              content.includes('cc.game') ||
              content.includes('Application') ||
              content.includes('legacyCC') ||
              content.includes('cclegacy')) {
            return true;
          }
        } catch (err) {
          // 忽略读取错误，继续检查其他文件
        }
      }
    }
    return false;
  }

  // 如果用户提供了版本提示，优先使用对应版本的路径
  if (versionHint === '3.8.x') {
    const settings38 = findExistingPath(paths38x.settings);
    const project38 = findExistingPath(paths38x.project);
    const res38 = findExistingPath(paths38x.res);

    if (settings38 && project38 && res38) {
      logger.info('使用用户指定的Cocos Creator 3.8.x项目结构');
      return {
        version: '3.8.x',
        settingsPath: settings38,
        projectPath: project38,
        resPath: res38
      };
    } else {
      logger.warn('用户指定3.8.x版本，但未找到对应文件结构，尝试自动检测...');
    }
  } else if (versionHint === '2.4.x') {
    const settings24 = findExistingPath(paths24x.settings);
    const project24 = findExistingPath(paths24x.project);
    const res24 = findExistingPath(paths24x.res);
    
    if (settings24 && project24 && res24) {
      logger.info('使用用户指定的Cocos Creator 2.4.x项目结构');
      return {
        version: '2.4.x',
        settingsPath: settings24,
        projectPath: project24,
        resPath: res24
      };
    } else {
      logger.warn('用户指定2.4.x版本，但未找到对应文件结构，尝试自动检测...');
    }
  } else if (versionHint === '2.3.x') {
    const settings23 = findExistingPath(paths23x.settings);
    const project23 = findExistingPath(paths23x.project);
    const res23 = findExistingPath(paths23x.res);
    
    if (settings23 && project23 && res23) {
      logger.info('使用用户指定的Cocos Creator 2.3.x项目结构');
      return {
        version: '2.3.x',
        settingsPath: settings23,
        projectPath: project23,
        resPath: res23
      };
    } else {
      logger.warn('用户指定2.3.x版本，但未找到对应文件结构，尝试自动检测...');
    }
  }

  // 自动检测：首先尝试3.8.x版本（最新版本优先）
  if (detect38xVersion()) {
    const settings38 = findExistingPath(paths38x.settings);
    const project38 = findExistingPath(paths38x.project);
    const res38 = findExistingPath(paths38x.res);

    if (settings38 && project38 && res38) {
      logger.info('自动检测到Cocos Creator 3.8.x项目结构');
      return {
        version: '3.8.x',
        settingsPath: settings38,
        projectPath: project38,
        resPath: res38
      };
    }
  }

  // 然后尝试2.3.x路径（更精确的检测）
  const settings23 = findExistingPath(paths23x.settings);
  const project23 = findExistingPath(paths23x.project);
  const res23 = findExistingPath(paths23x.res);

  if (settings23 && project23 && res23) {
    logger.info('自动检测到Cocos Creator 2.3.x或更早版本项目结构');
    return {
      version: '2.3.x',
      settingsPath: settings23,
      projectPath: project23,
      resPath: res23
    };
  }

  // 最后尝试2.4.x路径
  const settings24 = findExistingPath(paths24x.settings);
  const project24 = findExistingPath(paths24x.project);
  const res24 = findExistingPath(paths24x.res);

  if (settings24 && project24 && res24) {
    logger.info('自动检测到Cocos Creator 2.4.x项目结构');
    return {
      version: '2.4.x',
      settingsPath: settings24,
      projectPath: project24,
      resPath: res24
    };
  }

  // 如果都找不到，抛出详细错误信息
  throw new Error(`无法检测到有效的Cocos Creator项目结构，请检查输入路径是否正确。
支持的文件结构：
3.8.x: application.js/index.js + cc.js/index.js + assets/res目录
2.4.x: main.js/settings.js + project.js/main.js + assets/res目录
2.3.x: src/settings.js + src/project.js + res目录`);
}

/**
 * 验证路径是否存在
 * @param {string} resPath 资源路径
 * @param {string} settingsPath 设置文件路径
 * @param {string} projectPath 项目文件路径
 */
function validatePaths(resPath, settingsPath, projectPath) {
  if (!fs.existsSync(resPath)) {
    throw new Error(`错误: 资源路径不存在: ${resPath}`);
  }
  
  if (!fs.existsSync(settingsPath)) {
    throw new Error(`错误: settings.js 文件不存在: ${settingsPath}`);
  }
  
  if (!fs.existsSync(projectPath)) {
    throw new Error(`错误: project.js 文件不存在: ${projectPath}`);
  }
}

/**
 * 解析设置文件
 * @param {Buffer} settings 设置文件内容
 */
function parseSettings(settings) {
  try {
    const settingsContent = settings.toString('utf-8');

    // 根据版本使用不同的解析方式
    if (global.cocosVersion === '3.8.x') {
      // 3.8.x版本的解析逻辑
      if (settingsContent.includes('window._CCSettings')) {
        // Cocos Creator 3.x 使用 _CCSettings
        let _ccsettings = "let window = {_CCSettings: {}};" + settingsContent.split(';')[0];
        global.settings = eval(_ccsettings);
      } else if (settingsContent.includes('CCSettings')) {
        // 兼容旧的CCSettings格式
        let _ccsettings = "let window = {CCSettings: {}};" + settingsContent.split(';')[0];
        global.settings = eval(_ccsettings);
      } else {
        // 尝试解析为ES6模块或其他格式
        try {
          // 处理ES6模块导出
          if (settingsContent.includes('export') || settingsContent.includes('import')) {
            // 简单的ES6模块转换
            let moduleContent = settingsContent
              .replace(/export\s+default\s+/g, 'window._CCSettings = ')
              .replace(/export\s+/g, 'window.')
              .replace(/import\s+.*?from\s+.*?;/g, '');
            global.settings = eval("let window = {}; " + moduleContent + "; window");
          } else {
            global.settings = eval("let window = {}; " + settingsContent + "; window");
          }
        } catch (e) {
          logger.warn('3.8.x设置文件解析失败，使用默认设置');
          global.settings = { _CCSettings: {} };
        }
      }
    } else if (global.cocosVersion === '2.4.x') {
      // 2.4.x版本的解析逻辑
      if (settingsContent.includes('window.CCSettings')) {
        // 标准的CCSettings格式
        let _ccsettings = "let window = {CCSettings: {}};" + settingsContent.split(';')[0];
        global.settings = eval(_ccsettings);
      } else {
        // 尝试直接解析为对象
        try {
          global.settings = eval("let window = {}; " + settingsContent + "; window");
        } catch (e) {
          logger.warn('2.4.x设置文件解析失败，使用默认设置');
          global.settings = { CCSettings: {} };
        }
      }
    } else {
      // 2.3.x及以下版本的原有解析逻辑
      let _ccsettings = "let window = {CCSettings: {}};" + settingsContent.split(';')[0];
      global.settings = eval(_ccsettings);
    }
    
    // 确保settings不为空
    if (!global.settings || !global.settings.CCSettings) {
      global.settings = { CCSettings: {} };
    }
    
    if (global.verbose) {
      logger.debug('已加载项目设置:', Object.keys(global.settings.CCSettings || {}));
    }
  } catch (err) {
    logger.error('解析设置文件时出错:', err);
    logger.warn('使用默认设置');
    global.settings = { CCSettings: {} };
  }
}

module.exports = { reverseProject }; 