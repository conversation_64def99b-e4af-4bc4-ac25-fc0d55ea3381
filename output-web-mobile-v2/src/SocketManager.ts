/*
 * Auto-generated from SocketManager
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { GameEvents } from './GameEvents.ts';
import { EventManager } from './EventManager.ts';

var h,l;a._RF.push({},"534fbmJaVNBN6bGp9X0YpzD","SocketManager",void 0);var d=s.ccclass;e("SocketManager",d("SocketManager")(((l=function(e){function a(){for(var t,n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return(t=e.call.apply(e,[this].concat(a))||this)._socket=null,t._socketState=r.CLOSED,t._isAuthenticated=!1,t._reconnectAttempts=0,t._maxReconnectAttempts=5,t._reconnectDelay=3e3,t._heartbeatInterval=1e4,t._heartbeatTimer=null,t._lastMessageTime=0,t._connectionHealthTimer=null,t._backgroundHeartbeatInterval=15e3,t._lastHeartbeatSent=0,t._isPageVisible=!0,t._isIframeFocused=!0,t._lastFocusTime=Date.now(),t._syncOnFocusTimer=null,t._eventTarget=new o,t._messageQueue=[],t._authData={token:"",playerId:"",sessionId:"",balance:0,isAuthenticated:!1},t._config={socketUrl:"wss://gfty.spon-mini.com/fty_apartment_01",reconnectAttempts:5,reconnectDelay:3e3,heartbeatInterval:1e4,requestTimeout:1e4},t}t(a,e);var s=a.prototype;return s.start=function(){a._instance=this,this.privateSetupFocusManagement(),console.log("[SocketManager] Initialized")},a.GetInstance=function(){return this._instance},s.ConnectWithToken=function(e,t){var n=this;return void 0===t&&(t=""),new Promise((function(a,s){if(n._socketState===r.OPEN)return console.log("[SocketManager] Already connected"),void a(!0);n._authData.token=e,n._authData.playerId=t;try{console.log("[SocketManager] Connecting to:",n._config.socketUrl),n._socket=new WebSocket(n._config.socketUrl),n._socketState=r.CONNECTING,n._socket.onopen=function(){console.log("[SocketManager] WebSocket connected"),n._socketState=r.OPEN,n._reconnectAttempts=0,n._sendLoginRequest(),n._startHeartbeat(),n._processMessageQueue(),a(!0)},n._socket.onmessage=function(e){n._handleMessage(e)},n._socket.onclose=function(e){console.log("[SocketManager] WebSocket closed:",e.code,e.reason),n._socketState=r.CLOSED,n._isAuthenticated=!1,n._stopHeartbeat(),1e3!==e.code&&1001!==e.code&&n._handleReconnection()},n._socket.onerror=function(e){console.error("[SocketManager] WebSocket error:",e),n._emitError({code:-1,message:"WebSocket connection error",type:"connection",timestamp:(new Date).toISOString()}),s(new Error("WebSocket connection failed"))}}catch(e){console.error("[SocketManager] Connection failed:",e),s(e)}