/*
 * Auto-generated from SoundToggleButton
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { GameManager } from './GameManager.ts';
import { EventManager } from './EventManager.ts';

var d,f,m,M,B,I,b,v,y,S,E,O,G,T,F,z,W;a._RF.push({},"73d8dtBfUlLn6SQO/djhFKI","SoundToggleButton",void 0);var k=r.ccclass,w=r.property;e("SoundToggleButton",(d=k("SoundToggleButton"),f=w(s),m=w(u),M=w(g),B=w(g),I=w(g),b=w(g),v=w(h),d((E=n((S=function(e){function n(){for(var n,t=arguments.length,a=new Array(t),r=0;r<t;r++)a[r]=arguments[r];return n=e.call.apply(e,[this].concat(a))||this,o(n,"toggleButton",E,i(n)),o(n,"iconSprite",O,i(n)),o(n,"soundOnIcon",G,i(n)),o(n,"soundOffIcon",T,i(n)),o(n,"soundHoverWhenOffIcon",F,i(n)),o(n,"soundHoverWhenOnIcon",z,i(n)),o(n,"gameManager",W,i(n)),n.spriteFrameBackup=null,n}t(n,e);var a=n.prototype;return a.start=function(){if(this.gameManager=h.getInstance(),!this.gameManager)return p.instance.on("game-started",this.onGameManagerReady,this),void console.log("[SoundToggleButton] Waiting for GameManager...");this.setupButton(),this.updateIconWhenGameStarted()},a.setupButton=function(){this.toggleButton&&(this.toggleButton.node.on(s.EventType.CLICK,this.onToggleClick,this),this.toggleButton.node.on(c.EventType.MOUSE_ENTER,this.onMouseEnter,this),this.toggleButton.node.on(c.EventType.MOUSE_LEAVE,this.onMouseLeave,this))},a.onGameManagerReady=function(){this.gameManager=h.getInstance(),this.gameManager&&(this.setupButton(),this.updateIcon(),p.instance.off("game-started",this.onGameManagerReady,this))},a.onMouseLeave=function(){this.iconSprite.spriteFrame=this.spriteFrameBackup},a.onMouseEnter=function(){this.iconSprite.spriteFrame=this.spriteFrameBackup,this.iconSprite.spriteFrame=this.gameManager.IsBGMEnabled()?this.soundHoverWhenOnIcon:this.soundHoverWhenOffIcon},a.onToggleClick=function(){var e=this.gameManager.ToggleBGM();console.log("[SoundToggleButton] BGM toggled: "+(e?"ON":"OFF")),this.updateIcon()},a.updateIcon=function(){if(this.iconSprite&&this.gameManager){var e=this.gameManager.IsBGMEnabled();e&&this.soundOnIcon?this.iconSprite.spriteFrame=this.soundOnIcon:!e&&this.soundOffIcon&&(this.iconSprite.spriteFrame=this.soundOffIcon),this.spriteFrameBackup=this.iconSprite.spriteFrame}},a.updateIconWhenGameStarted=function(){this.node.active=!1,this.updateIcon(),this.node.active=!0},a.setBGMEnabled=function(e){this.gameManager&&(this.gameManager.IsBGMEnabled()!==e&&(this.gameManager.ToggleBGM(),this.updateIcon()))},a.getBGMEnabled=function(){return!!this.gameManager&&this.gameManager.IsBGMEnabled()},a.onDestroy=function(){this.toggleButton&&this.toggleButton.node.off(s.EventType.CLICK,this.onToggleClick,this)},n}(l)).prototype,"toggleButton",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=n(S.prototype,"iconSprite",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=n(S.prototype,"soundOnIcon",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=n(S.prototype,"soundOffIcon",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=n(S.prototype,"soundHoverWhenOffIcon",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=n(S.prototype,"soundHoverWhenOnIcon",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),W=n(S.prototype,"gameManager",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=S))||y));a._RF.pop()}}