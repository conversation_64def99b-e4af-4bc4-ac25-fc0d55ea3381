/*
 * Auto-generated from TowerAnimation
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { GameEvents } from './GameEvents.ts';
import { EventManager } from './EventManager.ts';
import { WorkersAnimation } from './WorkersAnimation.ts';
import { DynamicRandomPhaseConfig } from './DynamicRandomPhaseConfig.ts';

var S,v,R,D,C,N,x,M,I,Y,L,k,O,E,F,W,_,z,U,B,H;l._RF.push({},"1659aH/L1NO/LEOHX4u5qIU","TowerAnimation",void 0);var G=h.ccclass,j=h.property;e("TowerAnimation",(S=G("TowerAnimation"),v=j(b),R=j(f),D=j({tooltip:"Tower player identifier (A, P, T)"}),C=j({tooltip:"Horizontal jiggle amplitude (legacy apartment wobble)",range:[0,10,.1],slide:!0}),N=j({tooltip:"Seconds between jiggle flips (legacy apartment wobble)",range:[.01,.5,.01],slide:!0}),x=j({tooltip:"Optional smoothing window; set to 0 for instant jiggle",range:[0,.5,.01],slide:!0}),M=j({tooltip:"Strength of the speed-follow behaviour (0 = disabled)",range:[0,3,.1],slide:!0}),I=j({tooltip:"Reference vertical speed that keeps the base jiggle timing (units/sec)",range:[5,80,1],slide:!0}),Y=j({tooltip:"Smoothing used when sampling tower speed for wobble follow",range:[0,1,.05],slide:!0}),S((O=t((k=function(e){function t(){for(var t,i=arguments.length,r=new Array(i),a=0;a<i;a++)r[a]=arguments[a];return t=e.call.apply(e,[this].concat(r))||this,o(t,"towerNode",O,n(t)),o(t,"workersAnimation",E,n(t)),o(t,"towerPlayer",F,n(t)),o(t,"wobbleAmplitude",W,n(t)),o(t,"wobbleInterval",_,n(t)),o(t,"wobbleSmoothing",z,n(t)),o(t,"wobbleSpeedFollowStrength",U,n(t)),o(t,"wobbleSpeedReference",B,n(t)),o(t,"wobbleSpeedSmoothing",H,n(t)),t.isRacing=!1,t.activeTween=null,t.finalPosition=1,t.raceStartTime=0,t.currentPhase="phase1",t.currentY=g.RESET_Y,t.wobbleActive=!1,t.wobbleBaseX=0,t.wobbleOffset=0,t.wobbleTargetOffset=0,t.wobbleDirection=1,t.wobbleTimer=0,t.wobbleCurrentAmplitude=0,t.wobbleCurrentInterval=.02,t.wobbleSmoothedSpeed=0,t.wobbleSampledY=0,t.dynamicRaceConfig=null,t.currentActionIndex=0,t.raceDurationMultiplier=1,t.plannedDurationMs=null,t.countdownTween=null,t}i(t,e);var l=t.prototype;return l.debugLog=function(){var e;g.PERFORMANCE.ENABLE_DEBUG_LOGGING&&(e=console).log.apply(e,arguments)},l.onLoad=function(){this.debugLog("[TowerAnimation] "+this.towerPlayer+" onLoad() called - FORCING timing configuration"),T(8,10),this.towerNode&&this.resetPosition()},l.resetPosition=function(){this.towerNode&&(this.cancelCountdownTween(),this.currentY=g.RESET_Y,this.towerNode.setPosition(this.towerNode.position.x,this.currentY,this.towerNode.position.z),this.currentPhase="phase1",this.isRacing=!1,this.wobbleBaseX=this.towerNode.position.x,this.wobbleOffset=0,this.wobbleTargetOffset=0,this.wobbleTimer=0,this.wobbleDirection=-1,this.wobbleCurrentAmplitude=this.wobbleAmplitude,this.wobbleCurrentInterval=Math.max(.005,this.wobbleInterval),this.wobbleSmoothedSpeed=0,this.wobbleSampledY=this.currentY,this.applyTowerWobble(!0),this.debugLog("[TowerAnimation] "+this.towerPlayer+" reset to Y="+this.currentY))},l.cancelCountdownTween=function(){this.countdownTween&&(this.countdownTween.stop(),this.countdownTween=null)},l.animateToStartPosition=function(e,t){var i=this;if(this.towerNode&&!this.isRacing){var o=this.resolveCountdownTweenDuration(e,t);if(0!==o){var n=g.START_Y,r=this.towerNode.getPosition();Math.abs(r.y-n)<=.01?this.currentY=n:(this.cancelCountdownTween(),this.countdownTween=u(this.towerNode).to(o,{position:new c(r.x,n,r.z)},{easing:"sineOut",onUpdate:function(){i.currentY=i.towerNode.position.y,i.wobbleSampledY=i.currentY}}).call((function(){i.currentY=n,i.wobbleSampledY=i.currentY,i.countdownTween=null