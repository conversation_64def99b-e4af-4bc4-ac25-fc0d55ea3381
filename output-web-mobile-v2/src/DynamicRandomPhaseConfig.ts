/*
 * Auto-generated from DynamicRandomPhaseConfig
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';

e({assignWorkerReveals:l,calculateDynamicTargetPositions:function(e){for(var n,a=[],i=o.START_Y,E=t(e);!(n=E()).done;){var A=n.value;i+=o.TOTAL_DISTANCE*A.distancePercent,a.push(i)}a.length>0&&(a[a.length-1]=o.FINISH_Y);return a},calculateStaggeredTiming:c,generateDynamicPhases:R,generateDynamicRaceConfig:function(e,n){var a,i,E=R(),A=_(),r=A.minDuration,s=A.maxDuration,S=1;if(void 0!==(null==n?void 0:n.totalDuration))i=Math.min(Math.max(n.totalDuration,r),s),a=i,console.log("[DynamicRace] Position "+e+" duration override applied: "+(i/1e3).toFixed(2)+"s");else if(a=I(),S=c(e),(i=a*S)<r?(console.log("[DynamicRace] Position "+e+" total duration "+(i/1e3).toFixed(2)+"s clamped to min "+(r/1e3).toFixed(2)+"s"),i=r):i>s&&(console.log("[DynamicRace] Position "+e+" total duration "+(i/1e3).toFixed(2)+"s clamped to max "+(s/1e3).toFixed(2)+"s"),i=s),i===r&&e>1){var u=120*(e-1);i=Math.min(r+u,s)}var N=i/a;console.log("[DynamicRace] TIMING DEBUG - Position "+e+":",{baseDuration:(a/1e3).toFixed(2)+"s",staggeredMultiplier:N.toFixed(3),totalDuration:(i/1e3).toFixed(2)+"s",timingConfig:A}),console.log("[DynamicRace] Position "+e+" race config:",{timingRange:A.minDuration/1e3+"-"+A.maxDuration/1e3+"s",baseDuration:(a/1e3).toFixed(2)+"s",staggeredMultiplier:N.toFixed(3),totalDuration:(i/1e3).toFixed(2)+"s"});var C=T(E,i),M=C.reduce((function(e,n){return e+n.duration}),0),L=C.reduce((function(e,n){return e+Math.max(n.delayBefore,0)}),0),d=C.reduce((function(e,n){var t;return e+(null!=(t=n.microPauseDuration)?t:0)}),0),D=M+L+d;console.log("[DynamicRace] Position "+e+" DURATION VERIFICATION:"),console.log("  Expected total: "+(i/1e3).toFixed(2)+"s"),console.log("  Movement total: "+(M/1e3).toFixed(2)+"s"),console.log("  Delay total: "+(L/1e3).toFixed(2)+"s"),console.log("  Micro-pause total: "+(d/1e3).toFixed(2)+"s"),console.log("  Calculated total: "+(D/1e3).toFixed(2)+"s"),console.log("  Difference: "+((D-i)/1e3).toFixed(2)+"s");var P=C.map((function(e){return e.actionId+":"+e.easing+"("+e.delayBefore+"ms)"