/*
 * Auto-generated from CardSkin
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';

var m,f,C,S,k,b,g,v,y,w,B,F,A,T,D,z,I,x,G,M,N,_,J;n._RF.push({},"3f15aJ3bVdGp6gST8FaZTOS","CardSkin",void 0);var L=o.ccclass,P=o.property;a("default",(m=L("CardSkin"),f=P(s),C=P(d),S=P(d),k=P(d),b=P(d),g=P(d),v=P(d),y=P(d),m((F=e((B=function(a){function e(){for(var e,i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];return e=a.call.apply(a,[this].concat(n))||this,r(e,"cardSprite",F,t(e)),r(e,"useSprite",A,t(e)),r(e,"cardBack",T,t(e)),r(e,"cardDragonBack",D,t(e)),r(e,"cardTigerBack",z,t(e)),r(e,"heartCards",I,t(e)),r(e,"diamondCards",x,t(e)),r(e,"clubCards",G,t(e)),r(e,"spadeCards",M,t(e)),e.currentCard=null,e.isCardBack=!0,r(e,"enableAnimation",N,t(e)),r(e,"defaultFlipDuration",_,t(e)),r(e,"flipDirection",J,t(e)),e.currentTween=null,e.isAnimating=!1,e.animationCallbacks=[],e}i(e,a);var n=e.prototype;return n.onLoad=function(){!this.cardSprite&&this.getComponent(s)&&(this.cardSprite=this.getComponent(s))},n.SetCard=function(a){if(a&&this.useSprite&&this.cardSprite){this.currentCard=a,this.isCardBack=!1;var e=this.privateGetCardSpriteFrame(a);e?(this.cardSprite.spriteFrame=e,console.log("[CardSkin] Set card: "+a.display)):l("[CardSkin] Sprite frame not found for card: "+a.display)}else l("[CardSkin] Invalid card data or sprite not available")},n.SetCardByName=function(a,e){var i=this.privateCreateGameCard(a,e);i?this.SetCard(i):l("[CardSkin] Invalid card name: "+a+"_"+e)},n.SetCardByIndex=function(a){if(a<0||a>51)l("[CardSkin] Card index out of range (0-51)");else{var e=Math.floor(a/13),i=a%13;this.SetCardByName(["hearts","diamonds","clubs","spades"][e],["2","3","4","5","6","7","8","9","T","J","Q","K","A"][i])}},n.ShowCardBack=function(a){var e,i,r;if(void 0===a&&(a="normal"),this.useSprite&&this.cardSprite){this.isCardBack=!0,this.currentCard=null;var t,n=null;switch(console.log("[CardSkin] "+this.node.name+" showCardBack called with backType: "+a),console.log("[CardSkin] "+this.node.name+" available frames - dragon: "+!!this.cardDragonBack+", tiger: "+!!this.cardTigerBack+", normal: "+!!this.cardBack),a){case"dragon":n=this.cardDragonBack,console.log("[CardSkin] "+this.node.name+" selected dragon back frame: "+((null==(e=n)?void 0:e.name)||"null"));break;case"tiger":n=this.cardTigerBack,console.log("[CardSkin] "+this.node.name+" selected tiger back frame: "+((null==(i=n)?void 0:i.name)||"null"));break;default:n=this.cardBack,console.log("[CardSkin] "+this.node.name+" selected normal back frame: "+((null==(r=n)?void 0:r.name)||"null"))}if(n)this.cardSprite.spriteFrame=n,console.log("[CardSkin] "+this.node.name+" applied "+a+" card back - final frame: "+(null==(t=this.cardSprite.spriteFrame)?void 0:t.name));else l("[CardSkin] "+this.node.name+" "+a+" card back sprite frame not assigned")}},n.privateGetCardSpriteFrame=function(a){var e=[];switch(a.suit){case"hearts":e=this.heartCards;break;case"diamonds":e=this.diamondCards;break;case"clubs":e=this.clubCards;break;case"spades":e=this.spadeCards;break;default:return null}var i=this.privateGetCardIndex(a.value);return i>=0&&i<e.length&&e[i]?e[i]:null},n.privateGetCardIndex=function(a){var e={2:0,3:1,4:2,5:3,6:4,7:5,8:6,9:7,10:8,T:8,11:9,J:9,12:10,Q:10,13:11,K:11,a:12,A:12,1:12};return console.log('[CardSkin] getCardIndex for value: "'+a+'" -> index: '+(void 0!==e[a]?e[a]:-1)),void 0!==e[a]?e[a]:-1},n.privateCreateGameCard=function(a,e){var i={heart:"hearts",hearts:"hearts",dia:"diamonds",diamond:"diamonds",diamonds:"diamonds",clover:"clubs",club:"clubs",clubs:"clubs",spade:"spades",spades:"spades"}[a.toLowerCase()];if(!i)return null;var r,t=e.toLowerCase();switch(t){case"a":r=1;break;case"11":case"j":r=11;break;case"12":case"q":r=12;break;case"13":case"k":r=13;break;default:if(r=parseInt(t),isNaN(r)||r<2||r>10)return null}return{suit:i,value:t,display:""+("a"===t?"A":"11"===t?"J":"12"===t?"Q":"13"===t?"K":t)+{hearts:"♥",diamonds:"♦",clubs:"♣",spades:"♠"}[i],numericValue:r}},n.RandomCard=function(){var a=Math.floor(52*Math.random());this.SetCardByIndex(a)},n.IsShowingBack=function(){return this.isCardBack},n.GetCurrentCard=function(){return this.currentCard},n.GetCardCount=function(){return 52},n.privatePerformFlip=function(a,e,i,r){if(void 0===e&&(e=this.defaultFlipDuration),void 0===r&&(r=!1),!this.cardSprite||!this.enableAnimation)return this.cardSprite.spriteFrame=a,this.isCardBack=r,void(i&&i());this.StopFlip(),this.isAnimating=!0;var t=this.cardSprite.node;this.performSimpleFlip(t,a,e,i,r)},n.performSimpleFlip=function(a,e,i,r,t){var n=this;void 0===t&&(t=!1);var o=a.scale.clone();this.currentTween=c(a).to(i/2,{scale:"horizontal"===this.flipDirection?new u(0,o.y,o.z):new u(o.x,0,o.z)},{easing:"quadOut"}).call((function(){n.cardSprite.spriteFrame=e,n.isCardBack=t