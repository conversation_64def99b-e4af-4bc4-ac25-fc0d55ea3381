/*
 * Auto-generated from GameEvents
 * Reverse engineered by cc-reverse
 */

import * as cc from 'cc';

var n;e({getPlayerFromPosition:o,getPositionOfPlayer:function(e,r){var n=e[0],o=e[1],E=e[2];switch(r){case t.A:return n;case t.P:return o;case t.T:return E;default:throw new Error("Unknown player: "+r)}},getWinnerFromPositions:function(e){return o(e,1)}}),r._RF.push({},"9422cZ5jwpDkKEgLsY3edEw","GameEvents",void 0);e("GameEventCode",function(e){return e[e.GAME_INIT=101]="GAME_INIT",e[e.GAME_STATUS_UPDATE=108]="GAME_STATUS_UPDATE",e[e.GAME_RESULT=109]="GAME_RESULT",e[e.BALANCE_UPDATE=204]="BALANCE_UPDATE",e}({