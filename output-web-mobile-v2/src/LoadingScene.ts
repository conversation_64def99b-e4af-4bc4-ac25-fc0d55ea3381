/*
 * Auto-generated from LoadingScene
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { EventManager } from './EventManager.ts';

var f,m,L,G,v,y,w;o._RF.push({},"2ae81A3nDZHuJVFuylRS/c6","LoadingScene",void 0);var R=c.ccclass,E=c.property;e("LoadingScene",(f=R("LoadingScene"),m=E(u),L=E(d),f((y=t((v=function(e){function t(){for(var t,s=arguments.length,i=new Array(s),r=0;r<s;r++)i[r]=arguments[r];return t=e.call.apply(e,[this].concat(i))||this,a(t,"progressBar",y,n(t)),a(t,"statusLabel",w,n(t)),t.isLoadingComplete=!1,t.hasError=!1,t.hasSceneLoadStarted=!1,t.hasReceivedGameStatus=!1,t.isPersistentRoot=!1,t.isWaitingForGameStatus=!1,t.isFinalized=!1,t}s(t,e);var o=t.prototype;return o.onLoad=function(){h.addPersistRootNode(this.node),this.isPersistentRoot=!0},o.start=function(){this.initializeProgressBar(),this.startResourceLoading(),console.log("[LoadingScene] Starting resource loading...")},o.CompleteLoading=function(){this.isLoadingComplete||(this.isLoadingComplete=!0),this.waitForGameStatus(),this.onGameStatusReceived()},o.ResetLoading=function(){this.hasError=!1,this.isLoadingComplete=!1,this.hasSceneLoadStarted=!1,this.hasReceivedGameStatus=!1,this.isWaitingForGameStatus=!1,this.isFinalized=!1,this.detachGameStatusListener(),this.progressBar.progress=0,this.updateStatusMessage("Initializing..."),this.startResourceLoading()},o.initializeProgressBar=function(){this.progressBar?(this.progressBar.progress=0,console.log("[LoadingScene] Progress bar initialized to 0%")):console.warn("[LoadingScene] Progress bar not assigned in editor")},o.startResourceLoading=function(){var e=i(r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.updateProgress(.1,"Initializing..."),e.next=4,this.delay(200);case 4:return e.next=6,this.loadGameAssets();case 6:return this.updateProgress(.4,"Loading assets..."),e.next=9,this.delay(300);case 9:return e.next=11,this.preloadMainScene();case 11:return this.updateProgress(.7,"Loading scenes..."),e.next=14,this.delay(200);case 14:return e.next=16,this.initializeGameSystems();case 16:return this.updateProgress(.9,"Preparing game..."),e.next=19,this.delay(200);case 19:this.onResourcesReady(),e.next=26;break;case 22:e.prev=22,e.t0=e.catch(0),console.error("[LoadingScene] Loading failed:",e.t0),this.handleError("Loading failed");case 26:case"end":return e.stop()}}),e,this,[[0,22]])