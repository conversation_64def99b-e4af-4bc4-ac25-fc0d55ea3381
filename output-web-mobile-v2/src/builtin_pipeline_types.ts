/*
 * Auto-generated from builtin_pipeline_types
 * Reverse engineered by cc-reverse
 */

import * as cc from 'cc';

e({fillRequiredBloom:d,fillRequiredColorGrading:p,fillRequiredFSR:c,fillRequiredFXAA:f,fillRequiredHBAO:function(e){void 0===e.enabled&&(e.enabled=!1);void 0===e.radiusScale&&(e.radiusScale=1);void 0===e.angleBiasDegree&&(e.angleBiasDegree=10);void 0===e.blurSharpness&&(e.blurSharpness=3);void 0===e.aoSaturation&&(e.aoSaturation=1);void 0===e.needBlur&&(e.needBlur=!1)},fillRequiredMSAA:t,fillRequiredPipelineSettings:function(e){e.msaa?t(e.msaa):e.msaa=r();void 0===e.enableShadingScale&&(e.enableShadingScale=!1);void 0===e.shadingScale&&(e.shadingScale=.5);e.bloom?d(e.bloom):e.bloom=u();e.toneMapping?v(e.toneMapping):e.toneMapping={material:null};e.colorGrading?p(e.colorGrading):e.colorGrading={enabled:!1,material:null,contribute:1,colorGradingMap:null};e.fsr?c(e.fsr):e.fsr={enabled:!1,material:null,sharpness:.8};e.fxaa?f(e.fxaa):e.fxaa={enabled:!1,material:null}},fillRequiredToneMapping:v,makeBloom:u,makeColorGrading:s,makeFSR:m,makeFXAA:b,makeHBAO:function(){return{enabled:!1,radiusScale:1,angleBiasDegree:10,blurSharpness:3,aoSaturation:1,needBlur:!1}},makeMSAA:r,makePipelineSettings:function(){return{msaa:r(),enableShadingScale:!1,shadingScale:.5,bloom:u(),toneMapping:{material:null},colorGrading:{enabled:!1,material:null,contribute:1,colorGradingMap:null},fsr:{enabled:!1,material:null,sharpness:.8},fxaa:{enabled:!1,material:null}}},makeToneMapping:g}),a._RF.push({},"cbf30kCUX9A3K+QpVC6wnzx","builtin-pipeline-types",void 0);var i=l.SampleCount;function r(){return{enabled:!1,sampleCount:i.X4}}function t(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.sampleCount&&(e.sampleCount=i.X4)}var o=e("BloomType",function(e){return e[e.KawaseDualFilter=0]="KawaseDualFilter",e[e.MipmapFilter=1]="MipmapFilter",e}({