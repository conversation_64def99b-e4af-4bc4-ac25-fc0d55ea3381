/*
 * Auto-generated from WebCommunicationManager
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { Singleton } from './Singleton.ts';

var s;t._RF.push({},"47f37Uzm/JE4Z2v4kJYwzDY","WebCommunicationManager",void 0);var c=a.ccclass;n("WebCommunicationManager",c("WebCommunicationManager")(s=r(s=function(n){function t(){return n.apply(this,arguments)||this}e(t,n);var a=t.prototype;return a.start=function(){console.log("[WebCommunicationManager] Initialized for iframe communication"),console.log("[WebCommunicationManager] Environment info:",this.getEnvironmentInfo())},a.sendCode3ResultScreen=function(){var n=(new Date).toISOString();this.sendPostMessage(3,{},n)},a.sendCode4PanelState=function(){var n=(new Date).toISOString();this.sendPostMessage(4,{},n)},a.sendPostMessage=function(n,e,o){try{if("undefined"!=typeof window&&window.parent&&window.parent!==window){var t={code:n};window.parent.postMessage(t,"*"),console.log("[WebCommunicationManager] ✅ Successfully sent Code #"+n+" to parent window"),console.log("[WebCommunicationManager] Code #"+n+" full payload:",t),console.log("[WebCommunicationManager] Code #"+n+" sent at: "+o)}else console.warn("[WebCommunicationManager] ⚠️ Not in iframe context - Code #"+n+" not sent"),console.log("[WebCommunicationManager] Code #"+n+" would have sent:",{code:n,dataKeys:Object.keys(e),timestamp:o,windowParent:"undefined"!=typeof window?!!window.parent:"no window",isIframe:!("undefined"==typeof window||!window.parent)&&window.parent!==window})}catch(t){console.error("[WebCommunicationManager] ❌ Failed to send Code #"+n+" to parent window:",t.message),console.error("[WebCommunicationManager] Code #"+n+" error details:",{errorType:t.constructor.name,errorMessage:t.message,code:n,timestamp:o,hasData:!!e}),console.log("[WebCommunicationManager] Code #"+n+" failed payload:",e)}},a.isInIframeContext=function(){try{return"undefined"!=typeof window&&window.parent&&window.parent!==window}catch(n){return console.warn("[WebCommunicationManager] Error checking iframe context:",n.message),!1}},a.getEnvironmentInfo=function(){try{return{hasWindow:"undefined"!=typeof window,hasParent:"undefined"!=typeof window&&!!window.parent,isIframe:this.isInIframeContext(),userAgent:"undefined"!=typeof navigator?navigator.userAgent:"unknown",timestamp:(new Date).toISOString()}}catch(n){return console.error("[WebCommunicationManager] Error getting environment info:",n.message),{error:n.message,timestamp:(new Date).toISOString()}}},a.onDestroy=function(){console.log("[WebCommunicationManager] Destroyed")},o(t,null,[{key:"instance",get:function(){return t.instance}}]),t}(i))||s)||s);t._RF.pop()}}