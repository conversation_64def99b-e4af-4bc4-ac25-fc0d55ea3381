/*
 * Auto-generated from OptimizedSpineComponent
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { SpineMemoryManager } from './SpineMemoryManager.ts';
import { SpineInitializer } from './SpineInitializer.ts';

var y,C,_,g,z,S,M,A,v,b,E,w;s._RF.push({},"8fa983TL8tG/6RMzThUs/gF","OptimizedSpineComponent",void 0);var I=p.ccclass,O=p.property,x=p.requireComponent;n("OptimizedSpineComponent",(y=I("OptimizedSpineComponent"),C=x(l.Skeleton),_=O({displayName:"Auto Register",tooltip:"Automatically register with SpineMemoryManager"}),g=O({displayName:"Preload Animation",tooltip:"Preload animation data to avoid runtime loading"}),z=O({displayName:"Cleanup On Disable",tooltip:"Cleanup resources when component is disabled"}),S=O({displayName:"Max Retry Attempts",tooltip:"Maximum retry attempts for failed operations"}),y(M=C((v=t((A=function(n){function t(){for(var t,e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return t=n.call.apply(n,[this].concat(r))||this,i(t,"autoRegister",v,o(t)),i(t,"preloadAnimation",b,o(t)),i(t,"cleanupOnDisable",E,o(t)),i(t,"maxRetryAttempts",w,o(t)),t._spineComponent=null,t._memoryManager=null,t._isInitialized=!1,t._retryCount=0,t._lastError=null,t}e(t,n);var s=t.prototype;return s.onLoad=function(){this.initializeComponent()},s.onEnable=function(){this._isInitialized&&this.registerWithMemoryManager()},s.onDisable=function(){this.cleanupOnDisable&&this.unregisterFromMemoryManager()},s.onDestroy=function(){this.cleanup()},s.initializeComponent=function(){var n=r(a().mark((function n(){return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,f.waitForSpine();case 3:if(n.sent){n.next=6;break}throw new Error("Spine WASM not loaded");case 6:if(this._spineComponent=this.getComponent(l.Skeleton),this._spineComponent){n.next=9;break}throw new Error("sp.Skeleton component not found");case 9:if(this._memoryManager=d.getInstance(),this.setupErrorHandling(),!this.preloadAnimation){n.next=14;break}return n.next=14,this.preloadAnimationData();case 14:this._isInitialized=!0,this._retryCount=0,this._lastError=null,this.autoRegister&&this.registerWithMemoryManager(),c("[OptimizedSpineComponent] Initialized successfully: "+this.node.name),n.next=24;break;case 21:n.prev=21,n.t0=n.catch(0),this.handleInitializationError(n.t0);case 24:case"end":return n.stop()}}),n,this,[[0,21]])