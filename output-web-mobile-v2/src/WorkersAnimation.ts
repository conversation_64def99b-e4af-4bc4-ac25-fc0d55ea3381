/*
 * Auto-generated from WorkersAnimation
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { DynamicRandomPhaseConfig } from './DynamicRandomPhaseConfig.ts';
import { EventManager } from './EventManager.ts';
import { GameEvents } from './GameEvents.ts';

var A,g,W,M,_,S,y,E,T,I,R,b,C,x,P,F,O,N,D,B,L,z,V,H,G,U;a._RF.push({},"bfb31qgZFBKjL+fatEcSu/R","WorkersAnimation",void 0);var K=s.ccclass,q=s.property;e("WorkersAnimation",(A=K("WorkersAnimation"),g=q({tooltip:"Tower player identifier (A, P, T) - determines which tower these workers belong to"}),W=q({type:l,tooltip:"First worker animation (starts immediately)"}),M=q({type:l,tooltip:"Second worker animation (revealed at height 65)"}),_=q({type:l,tooltip:"Third worker animation (revealed at height 200)"}),S=q({type:l,tooltip:"Dirt animation (shown at height 200)"}),y=q(h),E=q(h),T=q({tooltip:"Enable back-and-forth worker movement"}),I=q({tooltip:"Base movement speed for workers",range:[.01,5,.001],slide:!0}),R=q({tooltip:"Enable random speed variations for workers"}),b=q({tooltip:"Use frame-based movement instead of tween-based (more stable, apartment pattern)"}),A(((U=function(e){function i(){for(var i,t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return i=e.call.apply(e,[this].concat(r))||this,n(i,"towerPlayer",P,o(i)),n(i,"worker1Animation",F,o(i)),n(i,"worker2Animation",O,o(i)),n(i,"worker3Animation",N,o(i)),n(i,"dirtAnimation",D,o(i)),n(i,"glassUIOpacity",B,o(i)),n(i,"auraUIOpacity",L,o(i)),n(i,"enableWorkerMovement",z,o(i)),n(i,"movementSpeed",V,o(i)),n(i,"enableSpeedVariation",H,o(i)),n(i,"useFrameBasedMovement",G,o(i)),i._isInitialized=!1,i._activeWorkers=new Set,i._currentMode="idle",i._lastWorkerStartTimes=new Map,i._workerStates=new Map,i._currentHeight=0,i._hasCompletedRace=!1,i._allRacesCompleteProcessed=!1,i._shouldStopWorkers=!1,i._lastGameStatus=-1,i._winLoseAnimationTriggered=!1,i._inWinLoseMode=!1,i._worker1Movement=null,i._worker2Movement=null,i._worker3Movement=null,i._movementInitialized=!1,i._originalWorkerPositions=new Map,i.glassTween=null,i.auraTween=null,i}t(i,e);var a=i.prototype;return a.onLoad=function(){this.initializeWorkers(),this.initializeWorkerStates(),this.setupEventListeners(),this._isInitialized=!0},a.onDestroy=function(){this.cleanup()},a.initializeWorkers=function(){try{this.captureOriginalWorkerPositions(),this.showAndStartWorker(this.worker1Animation,"worker1")||this.setWorkerToRestPose(this.worker1Animation,"worker1"),this.hideWorker(this.worker2Animation),this.hideWorker(this.worker3Animation),this.hideWorker(this.dirtAnimation),this.enableWorkerMovement&&this.initializeMovementState(),this.restoreAllWorkersToOriginalPositions(),this.forceAllAnimationsToPingPong()}catch(e){this._isInitialized=!1}},a.initializeWorkerStates=function(){var e,i,t=f.START_Y,n=f.WORKER_INTEGRATION.HEIGHT_THRESHOLDS.WORKER2_HEIGHT,o=f.WORKER_INTEGRATION.HEIGHT_THRESHOLDS.WORKER3_HEIGHT,r=f.WORKER_INTEGRATION.HEIGHT_THRESHOLDS.DIRT_HEIGHT,a=Number.isFinite(t)?t:0,s=Number.isFinite(n)?n:a,l=Number.isFinite(o)?o:s,h=Number.isFinite(r)?r:l,m=null!=(e=null==(i=this.worker1Animation)||null==(i=i.node)?void 0:i.active)&&e,d=this._activeWorkers.has(this.worker1Animation),c=m||this._currentHeight>=a;this._workerStates.set("worker1",{id:"worker1",animation:this.worker1Animation,movement:null,heightThreshold:a,isVisible:m,isActive:d,shouldBeVisible:c}),this._workerStates.set("worker2",{id:"worker2",animation:this.worker2Animation,movement:null,heightThreshold:s,isVisible:!1,isActive:!1,shouldBeVisible:!1}),this._workerStates.set("worker3",{id:"worker3",animation:this.worker3Animation,movement:null,heightThreshold:l,isVisible:!1,isActive:!1,shouldBeVisible:!1}),this._workerStates.set("dirt",{id:"dirt",animation:this.dirtAnimation,movement:null,heightThreshold:h,isVisible:!1,isActive:!1,shouldBeVisible:!1})},a.setupEventListeners=function(){k.instance.on(v.TOWER_ALL_RACES_COMPLETE,this.onAllRacesComplete,this),k.instance.on(v.TOWER_INDIVIDUAL_COMPLETE,this.onTowerIndividualComplete,this),k.instance.on(v.GAME_RESET_UI,this.onGameResetUI,this)},a.cleanup=function(){k.instance.off(v.TOWER_ALL_RACES_COMPLETE,this.onAllRacesComplete,this),k.instance.off(v.TOWER_INDIVIDUAL_COMPLETE,this.onTowerIndividualComplete,this),k.instance.off(v.GAME_RESULT,this.onRaceResult,this),k.instance.off(v.GAME_RESET_UI,this.onGameResetUI,this),this.stopAllWorkers(),this.stopAllMovement()},a.startRacingMode=function(){if(this._isInitialized){this._currentMode;this.stopAllWorkers(),this._currentMode="racing",this.restoreAllWorkersToOriginalPositions(["worker1"]),this.resetWorkerMovementState("worker1"),this._hasCompletedRace=!1,this._allRacesCompleteProcessed=!1,this._shouldStopWorkers=!1,this._lastGameStatus=-1,this._winLoseAnimationTriggered=!1,this._inWinLoseMode=!1,this.showAndStartWorker(this.worker1Animation,"worker1"),this.ensureMinimumWorkerVisible("racing mode"),this.startAllWorkerMovement()}},a.startJiggleMode=function(){if(this._isInitialized){this._currentMode;this.stopAllWorkers(),this._currentMode="jiggle",this.restoreAllWorkersToOriginalPositions(),this.showAndStartWorker(this.worker1Animation,"worker1"),this.ensureMinimumWorkerVisible("jiggle mode"),this.startAllWorkerMovement()}},a.startIdleMode=function(){this._isInitialized&&"idle"!==this._currentMode&&(this._currentMode="idle",this.stopAllWorkers(),this.restoreAllWorkersToOriginalPositions(),f.WORKER_INTEGRATION.USE_HEIGHT_BASED_REVEALS&&this.startIdleModeWithHeightLogic())},a.startIdleModeWithHeightLogic=function(){if(this._currentHeight<=0)this.startIdleModeShowAll();else{this.updateWorkerVisibilityStates(),this.applyWorkerStates();Array.from(this._workerStates.values()).filter((function(e){return e.isVisible