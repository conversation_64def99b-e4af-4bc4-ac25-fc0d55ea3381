/*
 * Auto-generated from DynamicPhaseTest
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { TowerAnimation } from './TowerAnimation.ts';
import { DynamicRandomPhaseConfig } from './DynamicRandomPhaseConfig.ts';

var h,f,p,y,w,T,D,N,d,I,b,R,E,P,S;s._RF.push({},"98043yc75JCh4l+I0geTAkJ","DynamicPhaseTest",void 0);var v=l.ccclass,C=l.property;e("DynamicPhaseTest",(h=v("DynamicPhaseTest"),f=C(u),p=C(u),y=C(u),w=C({tooltip:"Auto-run test on start"}),T=C({tooltip:"Delay between tests (seconds)"}),D=C({tooltip:"Enable debug logging for tests"}),h((I=o((d=function(e){function o(){for(var o,n=arguments.length,a=new Array(n),r=0;r<n;r++)a[r]=arguments[r];return o=e.call.apply(e,[this].concat(a))||this,t(o,"towerAnimationA",I,i(o)),t(o,"towerAnimationP",b,i(o)),t(o,"towerAnimationT",R,i(o)),t(o,"autoRunTest",E,i(o)),t(o,"testDelay",P,i(o)),t(o,"enableDebugLogging",S,i(o)),o}n(o,e);var s=o.prototype;return s.onLoad=function(){var e=this;this.autoRunTest&&this.scheduleOnce((function(){e.runDynamicPhaseTest()}),1)},s.runDynamicPhaseTest=function(){console.log("=== DYNAMIC RANDOM PHASE ANIMATION TEST ===");var e=m.PERFORMANCE.ENABLE_DEBUG_LOGGING;m.PERFORMANCE.ENABLE_DEBUG_LOGGING=this.enableDebugLogging,console.log("\n1. GENERATING DYNAMIC CONFIGURATIONS:");var o=g(1),n=g(2),t=g(3);A(o,"Tower A"),A(n,"Tower P"),A(t,"Tower T"),console.log("\n2. DYNAMIC SYSTEM FEATURES:"),this.showDynamicSystemFeatures(),this.towerAnimationA&&this.towerAnimationP&&this.towerAnimationT?(console.log("\n3. RUNNING DYNAMIC ANIMATIONS:"),this.runAnimationTest()):console.log("\n3. SKIPPING ANIMATION TEST (No tower references)"),m.PERFORMANCE.ENABLE_DEBUG_LOGGING=e},s.showDynamicSystemFeatures=function(){console.log("Dynamic Random Phase System Features:"),console.log("  ✓ Random phases: 3-7 per race"),console.log("  ✓ Small actions: 2-5 per phase"),console.log("  ✓ Delay times: 80-150ms between actions"),console.log("  ✓ Realistic effects: hesitation, bursts, fatigue"),console.log("  ✓ Staggered completion with variation"),console.log("  ✓ Configurable 8-10 second timing range"),console.log("  ✓ Smooth sine-based easing profiles"),console.log("  ✓ Progress-based worker reveals"),console.log("  ✓ Advanced performance optimizations")},s.runAnimationTest=function(){var e=a(r().mark((function e(){var o,n=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,o=[this.towerAnimationA.startRace(1),this.towerAnimationP.startRace(2),this.towerAnimationT.startRace(3)],console.log("Starting dynamic phase races..."),e.next=5,Promise.all(o);case 5:console.log("All dynamic phase races completed!"),this.scheduleOnce((function(){n.logSystemInfo()}),1),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("Animation test failed:",e.t0);case 12:case"end":return e.stop()}}),e,this,[[0,9]])