/*
 * Auto-generated from StartPopupView
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { EventManager } from './EventManager.ts';

var w,d,f,N,v,g,y,b,P;n._RF.push({},"fa4aeAoOhpJW6VyIl39hsAp","StartPopupView",void 0);var m=r.ccclass,A=r.property;t("StartPopupView",(w=m("StartPopupView"),d=A(s),f=A(s),N=A(s),w((y=e((g=function(t){function e(){for(var e,o=arguments.length,n=new Array(o),r=0;r<o;r++)n[r]=arguments[r];return e=t.call.apply(t,[this].concat(n))||this,i(e,"characterNode",y,a(e)),i(e,"flowNode",b,a(e)),i(e,"startNode",P,a(e)),e}o(e,t);var n=e.prototype;return n.init=function(){this.flowNode.active=!1,this.startNode.active=!1,this.characterNode.active=!1,this.characterNode.setPosition(400,-69,0)},n.start=function(){this.init(),this.showCharacter()},n.onDestroy=function(){c(this.characterNode).stop(),c(this.flowNode).stop(),c(this.startNode).stop(),this.characterNode.active=!1,this.flowNode.active=!1,this.startNode.active=!1,p.instance.emit(h.STARTUP_POPUP_HIDDEN),this.node.destroy()},n.showCharacter=function(){var t=this;this.characterNode.active=!0,c(this.characterNode).to(1,{position:new l(165,-69,0)},{easing:"backOut"}).call((function(){t.showFlow()