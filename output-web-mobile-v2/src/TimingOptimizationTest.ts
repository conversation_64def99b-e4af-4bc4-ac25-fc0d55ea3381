/*
 * Auto-generated from TimingOptimizationTest
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { TowerAnimation } from './TowerAnimation.ts';
import { DynamicRandomPhaseConfig } from './DynamicRandomPhaseConfig.ts';

var d,S,y,w,b,v,R,A,P,E,C;r._RF.push({},"b13b5soicdOZZZA4QUG4Dhl","TimingOptimizationTest",void 0);var D=l.ccclass,z=l.property;e("TimingOptimizationTest",(d=D("TimingOptimizationTest"),S=z(c),y=z(u),w=z(u),b=z(u),d((A=o((R=function(e){function o(){for(var o,n=arguments.length,s=new Array(n),a=0;a<n;a++)s[a]=arguments[a];return o=e.call.apply(e,[this].concat(s))||this,t(o,"statusLabel",A,i(o)),t(o,"towerA",P,i(o)),t(o,"towerP",E,i(o)),t(o,"towerT",C,i(o)),o.towerAnimations=[],o}n(o,e);var r=o.prototype;return r.onLoad=function(){console.log("[TimingOptimizationTest] Initializing timing optimization test system"),this.initializeTowerAnimations(),this.updateStatusDisplay()},r.initializeTowerAnimations=function(){for(var e=[this.towerA,this.towerP,this.towerT],o=["A","P","T"],n=0;n<e.length;n++)if(e[n]){var t=e[n].getComponent(m);t&&(t.towerPlayer=o[n],this.towerAnimations.push(t))}console.log("[TimingOptimizationTest] Initialized "+this.towerAnimations.length+" tower animations")},r.testCustomTimingRange=function(){console.log("\n=== TEST 1: Custom Timing Range (8-10s) ==="),p(8,10);for(var e=0;e<5;e++){var o=f();console.log("Sample "+(e+1)+":"),console.log("  Dynamic: "+(o/1e3).toFixed(2)+"s")}this.updateStatusDisplay()},r.testEasingSmoothness=function(){console.log("\n=== TEST 2: Easing Smoothness Comparison ==="),console.log("OLD SYSTEM (Jerky):"),console.log("  Phase 1: quadOut (abrupt speed changes)"),console.log("  Phase 2: quadInOut (harsh transitions)"),console.log("  Phase 3: quadIn (sudden acceleration)"),console.log("\nNEW SYSTEM (Smooth):"),console.log("  Phase 1: sineOut (smooth acceleration)"),console.log("  Phase 2: sineInOut (gentle transitions)"),console.log("  Phase 3: sineIn (natural acceleration)"),console.log("\nSmooth easing profiles provide:"),console.log("  ✓ Natural movement curves"),console.log("  ✓ Reduced visual jarring"),console.log("  ✓ Better user experience"),console.log("  ✓ More realistic race dynamics")},r.testOptimizedRace=function(){var e=s(a().mark((function e(){var o;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("\n=== TEST 3: Optimized Race Test ==="),!(this.towerAnimations.length<3)){e.next=4;break}return console.warn("Not enough tower animations for race test"),e.abrupt("return");case 4:return p(8,10),this.towerAnimations.forEach((function(e){return e.resetPosition()