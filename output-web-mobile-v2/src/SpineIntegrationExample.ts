/*
 * Auto-generated from SpineIntegrationExample
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { OptimizedSpineComponent } from './OptimizedSpineComponent.ts';
import { SpineMemoryManager } from './SpineMemoryManager.ts';
import { SpineStartupManager } from './SpineStartupManager.ts';

var S,d,g,f,C,x,A,I,E;p._RF.push({},"4eeb1Ei40dFBIkH+OuPFXbI","SpineIntegrationExample",void 0);var v=r.ccclass,R=r.property;n("SpineIntegrationExample",(S=v("SpineIntegrationExample"),d=R(c),g=R({displayName:"Animation Names",tooltip:"List of animation names to cycle through"}),f=R({displayName:"Auto Play",tooltip:"Automatically start playing animations"}),S((A=e((x=function(n){function e(){for(var e,i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];return e=n.call.apply(n,[this].concat(a))||this,t(e,"spineComponent",A,o(e)),t(e,"animationNames",I,o(e)),t(e,"autoPlay",E,o(e)),e._currentAnimationIndex=0,e._isSystemReady=!1,e}i(e,n);var p=e.prototype;return p.onLoad=function(){this.node.on("spine-system-ready",this.onSpineSystemReady,this),this.node.on("spine-error",this.onSpineError,this)},p.start=function(){var n=this.getComponent(y);n&&n.isInitialized()?this.onSpineSystemReady():m("[SpineIntegrationExample] Waiting for Spine system to initialize...")},p.onDestroy=function(){this.node.off("spine-system-ready",this.onSpineSystemReady,this),this.node.off("spine-error",this.onSpineError,this)},p.onSpineSystemReady=function(){m("[SpineIntegrationExample] Spine system is ready!"),this._isSystemReady=!0,this.spineComponent&&this.initializeSpineComponent(),this.autoPlay&&this.startAutoPlay()},p.initializeSpineComponent=function(){var n=a(s().mark((function n(){var e,i;return s().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.spineComponent){n.next=3;break}return u("[SpineIntegrationExample] No spine component assigned"),n.abrupt("return");case 3:e=0,i=10;case 5:if(this.spineComponent.isReady()||!(e<i)){n.next=11;break}return n.next=8,this.waitForSeconds(.5);case 8:e++,n.next=5;break;case 11:this.spineComponent.isReady()?(m("[SpineIntegrationExample] Spine component is ready"),this.animationNames.length>0&&this.playAnimation(this.animationNames[0])):u("[SpineIntegrationExample] Spine component failed to initialize");case 12:case"end":return n.stop()}}),n,this)