/*
 * Auto-generated from WorkerAnimationDebugger
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { TowerAnimationController } from './TowerAnimationController.ts';

var a,u,g,h,f;i._RF.push({},"3caf2MAIapKuaFMFuNsJANF","WorkerAnimationDebugger",void 0);var w=l.ccclass,p=l.property;o("WorkerAnimationDebugger",(a=w("WorkerAnimationDebugger"),u=p(c),a((f=e((h=function(o){function e(){for(var e,t=arguments.length,i=new Array(t),l=0;l<t;l++)i[l]=arguments[l];return e=o.call.apply(o,[this].concat(i))||this,n(e,"towerController",f,r(e)),e}t(e,o);var i=e.prototype;return i.onLoad=function(){this.towerController||(this.towerController=this.node.getComponentInChildren(c)||this.node.getComponentInParent(c))},i.start=function(){this.runDiagnostics()},i.runDiagnostics=function(){console.log("🔍 === WORKER ANIMATION DIAGNOSTICS ==="),this.towerController?(console.log("✅ TowerAnimationController found"),this.checkBasicSetup(),this.checkWorkerComponents(),this.testBasicFunctionality(),console.log("🔍 === END DIAGNOSTICS ===")):console.error("❌ TowerAnimationController not found! Please assign it in the inspector.")},i.checkBasicSetup=function(){console.log("\n📋 Checking basic setup...");var o=this.towerController.getRaceState();console.log("Race state:",o);var e=this.towerController.getAllTowerStates();console.log("Tower states:",e)},i.checkWorkerComponents=function(){console.log("\n🔧 Checking WorkersAnimation components..."),this.towerController.debugAllWorkers()},i.testBasicFunctionality=function(){var o=this;console.log("\n🧪 Testing basic functionality..."),console.log("Testing force show worker1..."),this.towerController.forceShowAllWorker1(),setTimeout((function(){console.log("Testing height-based reveals..."),o.towerController.testAllTowerHeights()}),1e3)},i.testWorkerVisibility=function(){console.log("🎮 Manual worker visibility test"),this.towerController.forceShowAllWorker1()},i.testHeightSystem=function(){console.log("🎮 Manual height system test"),this.towerController.testAllTowerHeights()},i.testRacing=function(){console.log("🎮 Manual racing test"),this.towerController.testRacing()},i.showDebugInfo=function(){console.log("🎮 Debug info"),this.towerController.debugAllWorkers()},e}(s)).prototype,"towerController",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),g=h))||g));i._RF.pop()}}