/*
 * Auto-generated from builtin_pipeline_settings
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { builtin_pipeline_types } from './builtin-pipeline-types.ts';

var P,_,M,S,w,O,k,E,D,G,B,C,A,j,v,F,x,R,T,I,L,X,z,H,q,Y,N,Q,Z,J,K,U,V;s._RF.push({},"de1c2EHcMhAIYRZY5nyTQHG","builtin-pipeline-settings",void 0);var W=a.ccclass,$=a.disallowMultiple,tt=a.executeInEditMode,et=a.menu,ot=a.property,it=a.requireComponent,nt=a.type;t("BuiltinPipelineSettings",(P=W("BuiltinPipelineSettings"),_=et("Rendering/BuiltinPipelineSettings"),M=it(p),S=ot(l),w=ot({displayName:"Editor Preview (Experimental)",type:l}),O=ot({group:{id:"MSAA",name:"Multisample Anti-Aliasing"},type:l}),k=ot({group:{id:"MSAA",name:"Multisample Anti-Aliasing",style:"section"},type:g,range:[2,4,2]}),E=ot({group:{id:"ShadingScale",name:"ShadingScale",style:"section"},type:l}),D=ot({tooltip:"i18n:postprocess.shadingScale",group:{id:"ShadingScale",name:"ShadingScale"},type:c,range:[.01,4,.01],slide:!0}),G=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:l}),B=nt(b),C=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"}}),A=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:y}),j=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:y}),v=ot({tooltip:"i18n:bloom.enableAlphaMask",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:l}),F=ot({tooltip:"i18n:bloom.iterations",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:g,range:[1,6,1],slide:!0}),x=ot({tooltip:"i18n:bloom.threshold",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:c,min:0}),R=nt(c),T=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"}}),I=ot({group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:l}),L=ot({group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:y}),X=ot({tooltip:"i18n:color_grading.contribute",group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:c,range:[0,1,.01],slide:!0}),z=ot({tooltip:"i18n:color_grading.originalMap",group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:u}),H=ot({group:{id:"FXAA",name:"Fast Approximate Anti-Aliasing (PostProcessing)",style:"section"},type:l}),q=ot({group:{id:"FXAA",name:"Fast Approximate Anti-Aliasing (PostProcessing)",style:"section"},type:y}),Y=ot({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:l}),N=ot({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:y}),Q=ot({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:c,range:[0,1,.01],slide:!0}),Z=ot({group:{id:"ToneMapping",name:"ToneMapping",style:"section"},type:y}),P(J=_(J=M(J=$(J=tt((U=e((K=function(t){function e(){for(var e,o=arguments.length,r=new Array(o),s=0;s<o;s++)r[s]=arguments[s];return e=t.call.apply(t,[this].concat(r))||this,i(e,"_settings",U,n(e)),i(e,"_editorPreview",V,n(e)),e}o(e,t);var s=e.prototype;return s.getPipelineSettings=function(){return this._settings},s.onEnable=function(){h(this._settings),this.getComponent(p).camera.pipelineSettings=this._settings},s.onDisable=function(){var t=this.getComponent(p).camera;t&&(t.pipelineSettings=null)},s._tryEnableEditorPreview=function(){void 0!==m&&(this._editorPreview?m.setEditorPipelineSettings(this._settings):this._disableEditorPreview())},s._disableEditorPreview=function(){void 0!==m&&(m.getEditorPipelineSettings()===this._settings&&m.setEditorPipelineSettings(null))},r(e,[{key:"editorPreview",get:function(){return this._editorPreview},set:function(t){this._editorPreview=t}},{key:"MsaaEnable",get:function(){return this._settings.msaa.enabled},set:function(t){this._settings.msaa.enabled=t}},{key:"msaaSampleCount",get:function(){return this._settings.msaa.sampleCount},set:function(t){t=Math.pow(2,Math.ceil(Math.log2(Math.max(t,2)))),t=Math.min(t,4),this._settings.msaa.sampleCount=t}},{key:"shadingScaleEnable",get:function(){return this._settings.enableShadingScale},set:function(t){this._settings.enableShadingScale=t}},{key:"shadingScale",get:function(){return this._settings.shadingScale},set:function(t){this._settings.shadingScale=t}},{key:"bloomEnable",get:function(){return this._settings.bloom.enabled},set:function(t){this._settings.bloom.enabled=t}},{key:"bloomType",get:function(){return this._settings.bloom.type},set:function(t){this._settings.bloom.type=t}},{key:"kawaseBloomMaterial",get:function(){return this._settings.bloom.kawaseFilterMaterial},set:function(t){this._settings.bloom.kawaseFilterMaterial!==t&&(this._settings.bloom.kawaseFilterMaterial=t)}},{key:"mipmapBloomMaterial",get:function(){return this._settings.bloom.mipmapFilterMaterial},set:function(t){this._settings.bloom.mipmapFilterMaterial!==t&&(this._settings.bloom.mipmapFilterMaterial=t)}},{key:"bloomEnableAlphaMask",get:function(){return this._settings.bloom.enableAlphaMask},set:function(t){this._settings.bloom.enableAlphaMask=t}},{key:"bloomIterations",get:function(){return this._settings.bloom.iterations},set:function(t){this._settings.bloom.iterations=t}},{key:"bloomThreshold",get:function(){return this._settings.bloom.threshold},set:function(t){this._settings.bloom.threshold=t}},{key:"bloomIntensity",get:function(){return this._settings.bloom.intensity},set:function(t){this._settings.bloom.intensity=t}},{key:"colorGradingEnable",get:function(){return this._settings.colorGrading.enabled},set:function(t){this._settings.colorGrading.enabled=t}},{key:"colorGradingMaterial",get:function(){return this._settings.colorGrading.material},set:function(t){this._settings.colorGrading.material!==t&&(this._settings.colorGrading.material=t)}},{key:"colorGradingContribute",get:function(){return this._settings.colorGrading.contribute},set:function(t){this._settings.colorGrading.contribute=t}},{key:"colorGradingMap",get:function(){return this._settings.colorGrading.colorGradingMap},set:function(t){this._settings.colorGrading.colorGradingMap=t}},{key:"fxaaEnable",get:function(){return this._settings.fxaa.enabled},set:function(t){this._settings.fxaa.enabled=t}},{key:"fxaaMaterial",get:function(){return this._settings.fxaa.material},set:function(t){this._settings.fxaa.material!==t&&(this._settings.fxaa.material=t)}},{key:"fsrEnable",get:function(){return this._settings.fsr.enabled},set:function(t){this._settings.fsr.enabled=t}},{key:"fsrMaterial",get:function(){return this._settings.fsr.material},set:function(t){this._settings.fsr.material!==t&&(this._settings.fsr.material=t)}},{key:"fsrSharpness",get:function(){return this._settings.fsr.sharpness},set:function(t){this._settings.fsr.sharpness=t}},{key:"toneMappingMaterial",get:function(){return this._settings.toneMapping.material},set:function(t){this._settings.toneMapping.material!==t&&(this._settings.toneMapping.material=t)}}]),e}(d)).prototype,"_settings",[ot],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return f()}}),V=e(K.prototype,"_editorPreview",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),e(K.prototype,"editorPreview",[w],Object.getOwnPropertyDescriptor(K.prototype,"editorPreview"),K.prototype),e(K.prototype,"MsaaEnable",[O],Object.getOwnPropertyDescriptor(K.prototype,"MsaaEnable"),K.prototype),e(K.prototype,"msaaSampleCount",[k],Object.getOwnPropertyDescriptor(K.prototype,"msaaSampleCount"),K.prototype),e(K.prototype,"shadingScaleEnable",[E],Object.getOwnPropertyDescriptor(K.prototype,"shadingScaleEnable"),K.prototype),e(K.prototype,"shadingScale",[D],Object.getOwnPropertyDescriptor(K.prototype,"shadingScale"),K.prototype),e(K.prototype,"bloomEnable",[G],Object.getOwnPropertyDescriptor(K.prototype,"bloomEnable"),K.prototype),e(K.prototype,"bloomType",[B,C],Object.getOwnPropertyDescriptor(K.prototype,"bloomType"),K.prototype),e(K.prototype,"kawaseBloomMaterial",[A],Object.getOwnPropertyDescriptor(K.prototype,"kawaseBloomMaterial"),K.prototype),e(K.prototype,"mipmapBloomMaterial",[j],Object.getOwnPropertyDescriptor(K.prototype,"mipmapBloomMaterial"),K.prototype),e(K.prototype,"bloomEnableAlphaMask",[v],Object.getOwnPropertyDescriptor(K.prototype,"bloomEnableAlphaMask"),K.prototype),e(K.prototype,"bloomIterations",[F],Object.getOwnPropertyDescriptor(K.prototype,"bloomIterations"),K.prototype),e(K.prototype,"bloomThreshold",[x],Object.getOwnPropertyDescriptor(K.prototype,"bloomThreshold"),K.prototype),e(K.prototype,"bloomIntensity",[R,T],Object.getOwnPropertyDescriptor(K.prototype,"bloomIntensity"),K.prototype),e(K.prototype,"colorGradingEnable",[I],Object.getOwnPropertyDescriptor(K.prototype,"colorGradingEnable"),K.prototype),e(K.prototype,"colorGradingMaterial",[L],Object.getOwnPropertyDescriptor(K.prototype,"colorGradingMaterial"),K.prototype),e(K.prototype,"colorGradingContribute",[X],Object.getOwnPropertyDescriptor(K.prototype,"colorGradingContribute"),K.prototype),e(K.prototype,"colorGradingMap",[z],Object.getOwnPropertyDescriptor(K.prototype,"colorGradingMap"),K.prototype),e(K.prototype,"fxaaEnable",[H],Object.getOwnPropertyDescriptor(K.prototype,"fxaaEnable"),K.prototype),e(K.prototype,"fxaaMaterial",[q],Object.getOwnPropertyDescriptor(K.prototype,"fxaaMaterial"),K.prototype),e(K.prototype,"fsrEnable",[Y],Object.getOwnPropertyDescriptor(K.prototype,"fsrEnable"),K.prototype),e(K.prototype,"fsrMaterial",[N],Object.getOwnPropertyDescriptor(K.prototype,"fsrMaterial"),K.prototype),e(K.prototype,"fsrSharpness",[Q],Object.getOwnPropertyDescriptor(K.prototype,"fsrSharpness"),K.prototype),e(K.prototype,"toneMappingMaterial",[Z],Object.getOwnPropertyDescriptor(K.prototype,"toneMappingMaterial"),K.prototype),J=K))||J)||J)||J)||J)||J));s._RF.pop()}}