/*
 * Auto-generated from PopupManager
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { EventManager } from './EventManager.ts';
import { GameEvents } from './GameEvents.ts';

var d,R,m,S,v,y,g,b,w,M;s._RF.push({},"adbfeyEakhFJ7jyzhvynibp","PopupManager",void 0);var T=i.ccclass,E=i.property;t("PopupManager",(d=T("PopupManager"),R=E(u),m=E(u),S=E(p),d(((M=function(t){function e(){for(var e,n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return e=t.call.apply(t,[this].concat(a))||this,r(e,"startPopupPrefab",g,o(e)),r(e,"resultPopupPrefab",b,o(e)),r(e,"popupContainer",w,o(e)),e.currentStartPopup=null,e.currentResultPopup=null,e.latestGameResult=null,e}n(e,t),e.getInstance=function(){return this.instance};var s=e.prototype;return s.onLoad=function(){e.instance=this,this.registerListeners()},s.onDestroy=function(){this.detachListeners(),this.disposeNode(this.currentStartPopup),this.disposeNode(this.currentResultPopup),this.currentStartPopup=null,this.currentResultPopup=null,e.instance=null},s.registerListeners=function(){P.instance.on(f.TOWER_RESULT,this.onGameResult,this),P.instance.on(f.SHOW_RESULT_POPUP,this.onShowResultPopup,this),P.instance.on(f.GAME_RESET_UI,this.onGameResetUI,this),P.instance.on(f.STARTUP_POPUP_SHOWN,this.onStartupPopupShown,this)},s.detachListeners=function(){P.instance.off(f.TOWER_RESULT,this.onGameResult,this),P.instance.off(f.SHOW_RESULT_POPUP,this.onShowResultPopup,this),P.instance.off(f.GAME_RESET_UI,this.onGameResetUI,this),P.instance.off(f.STARTUP_POPUP_SHOWN,this.onStartupPopupShown,this)},s.disposeNode=function(t){t&&t.isValid&&t.destroy()},s.onGameResult=function(t){this.latestGameResult=t},s.onGameResetUI=function(){this.currentStartPopup=null,this.currentResultPopup=null,this.latestGameResult=null},s.onStartupPopupShown=function(){var t=this;this.startPopupPrefab?(this.disposeNode(this.currentStartPopup),setTimeout((function(){if(t.startPopupPrefab&&t.isValid){var e=l(t.startPopupPrefab);(t.popupContainer||t.node).addChild(e),t.currentStartPopup=e}}),200)):console.warn("[PopupManager] Start popup prefab not assigned")},s.onShowResultPopup=function(){var t=this.transformServerResultToRaceData(this.latestGameResult);t&&this.presentResultPopup(t)},s.presentResultPopup=function(t){if(this.resultPopupPrefab){var e=this.transformRaceDataForRankPopup(t);if(e){this.disposeNode(this.currentResultPopup);var n=l(this.resultPopupPrefab);(this.popupContainer||this.node).addChild(n),this.currentResultPopup=n;var r=n.getComponent("RankPopupView");r&&"function"==typeof r.SetupPopup?r.SetupPopup(e):console.warn("[PopupManager] RankPopupView component not found or not ready")}}else console.warn("[PopupManager] Result popup prefab not assigned")},s.transformRaceDataForRankPopup=function(t){if(!t||!Array.isArray(t.results)||3!==t.results.length)return console.warn("[PopupManager] Invalid race data for result popup:",t),null;for(var e,n={},r=a(t.results);!(e=r()).done;){var o=e.value;o.player&&"number"==typeof o.position&&(n[o.player]=o.position)}var s=[h.A,h.P,h.T].find((function(t){return void 0===n[t]