/*
 * Auto-generated from FakeLoadingView
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { EventManager } from './EventManager.ts';

var p,f,S,L,m,y,v;o._RF.push({},"d00cfokO59F4qytaqb1ILU6","FakeLoadingView",void 0);var P=u.ccclass,b=u.property;e("FakeLoadingView",(p=P("FakeLoadingView"),f=b(c),S=b(g),p((y=t((m=function(e){function t(){for(var t,s=arguments.length,i=new Array(s),n=0;n<s;n++)i[n]=arguments[n];return t=e.call.apply(e,[this].concat(i))||this,a(t,"progressBar",y,r(t)),a(t,"statusLabel",v,r(t)),t.isLoadingComplete=!1,t.hasError=!1,t.hasSceneLoadStarted=!1,t.hasReceivedGameStatus=!1,t.isPersistentRoot=!1,t.isWaitingForGameStatus=!1,t.isFinalized=!1,t}s(t,e);var o=t.prototype;return o.start=function(){this.initializeProgressBar(),this.startResourceLoading()},o.handleFirstGameStatusChanged=function(e){var t;console.log("[LoadingScene] GAME_STATUS_CHANGED received:",null!=(t=null==e?void 0:e.status)?t:"unknown"),this.node.destroy()},o.initializeProgressBar=function(){this.progressBar?(this.progressBar.progress=0,console.log("[LoadingScene] Progress bar initialized to 0%")):console.warn("[LoadingScene] Progress bar not assigned in editor")},o.startResourceLoading=function(){var e=i(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.updateProgress(.1,"Initializing..."),e.next=4,this.delay(200);case 4:return this.updateProgress(.4,"Loading assets..."),e.next=7,this.delay(300);case 7:return this.updateProgress(.7,"Loading scenes..."),e.next=10,this.delay(200);case 10:return this.updateProgress(.9,"Preparing game..."),e.next=13,this.delay(200);case 13:this.onResourcesReady(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),console.error("[LoadingScene] Loading failed:",e.t0);case 19:case"end":return e.stop()}}),e,this,[[0,16]])