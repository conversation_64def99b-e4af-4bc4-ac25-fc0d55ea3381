/*
 * Auto-generated from SpineStartupManager
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { SpineInitializer } from './SpineInitializer.ts';
import { SpineMemoryManager } from './SpineMemoryManager.ts';

var g,h,b,w,z,M,I,v,C,x,_;s._RF.push({},"ec5e5CrFoNB8pKhC/+tzZI9","SpineStartupManager",void 0);var A=u.ccclass,E=u.property;e("SpineStartupManager",(g=A("SpineStartupManager"),h=E({displayName:"Initialize On Start",tooltip:"Initialize Spine automatically when the scene starts"}),b=E({displayName:"Show Debug Info",tooltip:"Show debug information during initialization"}),w=E({displayName:"Retry On Failure",tooltip:"Retry initialization if it fails"}),z=E({displayName:"Max Retry Attempts",tooltip:"Maximum number of retry attempts"}),g((v=t((I=function(e){function t(){for(var t,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return t=e.call.apply(e,[this].concat(a))||this,n(t,"initializeOnStart",v,r(t)),n(t,"showDebugInfo",C,r(t)),n(t,"retryOnFailure",x,r(t)),n(t,"maxRetryAttempts",_,r(t)),t._isInitialized=!1,t._retryCount=0,t._initializationPromise=null,t}i(t,e);var s=t.prototype;return s.onLoad=function(){},s.start=function(){this.initializeOnStart&&this.initializeSpineSystem()},s.initializeSpineSystem=function(){var e=a(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._isInitialized){e.next=3;break}return f("[SpineStartupManager] Spine system already initialized"),e.abrupt("return",!0);case 3:if(!this._initializationPromise){e.next=8;break}return f("[SpineStartupManager] Initialization in progress, waiting..."),e.next=7,this._initializationPromise;case 7:return e.abrupt("return",e.sent);case 8:return this._initializationPromise=this.performInitialization(),e.next=11,this._initializationPromise;case 11:return e.abrupt("return",e.sent);case 12:case"end":return e.stop()}}),e,this)