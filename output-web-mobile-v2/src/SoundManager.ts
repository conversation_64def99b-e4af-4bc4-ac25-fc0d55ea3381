/*
 * Auto-generated from SoundManager
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { Singleton } from './Singleton.ts';

var f,d,h,p,S,b,m,O,M,T;l._RF.push({},"97eb3MQ+y9Bg7b9YAqkpqCw","SoundManager",void 0);var x=r.ccclass,y=r.property;e("SoundManager",(f=x("SoundManager"),d=y(a),h=y(a),p=y(a),f(S=u(((T=function(e){function o(){for(var o,n=arguments.length,s=new Array(n),l=0;l<n;l++)s[l]=arguments[l];return o=e.call.apply(e,[this].concat(s))||this,i(o,"bgmClip",m,t(o)),i(o,"sfxToggleOnClip",O,t(o)),i(o,"sfxToggleOffClip",M,t(o)),o.bgmSource=null,o.sfxToggleOnSource=null,o.sfxToggleOffSource=null,o.isBGMEnabled=!0,o}n(o,e);var l=o.prototype;return l.onLoad=function(){this.createAudioSources(),this.loadSoundPreference()},l.start=function(){console.log("[SoundManager] start() - bgmSource:",!!this.bgmSource,"bgmClip:",!!this.bgmClip),this.isBGMEnabled&&this.bgmSource&&this.bgmClip&&(this.bgmSource.clip=this.bgmClip,this.bgmSource.play(),console.log("[SoundManager] BGM started based on preference")),this.notifyParentWindow(this.isBGMEnabled)},l.createAudioSources=function(){this.bgmSource=this.node.addComponent(g),this.bgmSource&&(this.bgmSource.loop=!0,this.bgmSource.playOnAwake=!1,this.bgmSource.clip=this.bgmClip),this.sfxToggleOnSource=this.node.addComponent(g),this.sfxToggleOnSource&&(this.sfxToggleOnSource.loop=!1,this.sfxToggleOnSource.playOnAwake=!1,this.sfxToggleOnSource.clip=this.sfxToggleOnClip),this.sfxToggleOffSource=this.node.addComponent(g),this.sfxToggleOffSource&&(this.sfxToggleOffSource.loop=!1,this.sfxToggleOffSource.playOnAwake=!1,this.sfxToggleOffSource.clip=this.sfxToggleOffClip),console.log("[SoundManager] Audio sources created and clips assigned")},l.toggleBGM=function(){return this.isBGMEnabled=!this.isBGMEnabled,this.saveSoundPreference(),this.notifyParentWindow(this.isBGMEnabled),this.isBGMEnabled?(this.bgmSource&&this.bgmClip&&(this.bgmSource.clip=this.bgmClip,this.bgmSource.play()),this.sfxToggleOnSource&&this.sfxToggleOnClip&&(this.sfxToggleOnSource.clip=this.sfxToggleOnClip,this.sfxToggleOnSource.play())):(this.bgmSource&&this.bgmSource.stop(),this.sfxToggleOffSource&&this.sfxToggleOffClip&&(this.sfxToggleOffSource.clip=this.sfxToggleOffClip,this.sfxToggleOffSource.play())),console.log("[SoundManager] BGM "+(this.isBGMEnabled?"enabled":"disabled")),this.isBGMEnabled},l.getBGMEnabled=function(){return this.isBGMEnabled},l.isBGMPlaying=function(){var e;return(null==(e=this.bgmSource)?void 0:e.playing)||!1},l.setBGMEnabled=function(e){this.isBGMEnabled!==e&&(this.isBGMEnabled=e,this.saveSoundPreference(),this.notifyParentWindow(e),e&&this.bgmSource&&this.bgmClip?(this.bgmSource.clip=this.bgmClip,this.bgmSource.play()):!e&&this.bgmSource&&this.bgmSource.stop(),console.log("[SoundManager] BGM "+(e?"enabled":"disabled")))},l.playToggleSound=function(e){e&&this.sfxToggleOnSource&&this.sfxToggleOnClip?(this.sfxToggleOnSource.clip=this.sfxToggleOnClip,this.sfxToggleOnSource.play()):!e&&this.sfxToggleOffSource&&this.sfxToggleOffClip&&(this.sfxToggleOffSource.clip=this.sfxToggleOffClip,this.sfxToggleOffSource.play())},l.notifyParentWindow=function(e){try{if("undefined"!=typeof window&&window.parent&&window.parent!==window){var o=e?1:2;window.parent.postMessage({code:o},"*"),console.log("[SoundManager] Notified parent window: sound "+(e?"ON":"OFF")+" (code: "+o+")")}}catch(e){console.warn("[SoundManager] Failed to notify parent window:",e.message)}},l.loadSoundPreference=function(){try{if("undefined"!=typeof localStorage){var e=localStorage.getItem(o.SOUND_PREFERENCE_KEY);null!==e?(this.isBGMEnabled="true"===e,console.log("[SoundManager] Loaded sound preference: "+(this.isBGMEnabled?"enabled":"disabled"))):console.log("[SoundManager] No saved sound preference found, using default: enabled")}}catch(e){console.warn("[SoundManager] Failed to load sound preference from localStorage:",e.message),this.isBGMEnabled=!0}},l.saveSoundPreference=function(){try{"undefined"!=typeof localStorage&&(localStorage.setItem(o.SOUND_PREFERENCE_KEY,this.isBGMEnabled.toString()),console.log("[SoundManager] Saved sound preference: "+(this.isBGMEnabled?"enabled":"disabled")))}catch(e){console.warn("[SoundManager] Failed to save sound preference to localStorage:",e.message)}},s(o,null,[{key:"instance",get:function(){return o.instance}}]),o}(c)).SOUND_PREFERENCE_KEY="tower_game_sound_enabled",m=o((b=T).prototype,"bgmClip",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=o(b.prototype,"sfxToggleOnClip",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=o(b.prototype,"sfxToggleOffClip",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=b))||S)||S));l._RF.pop()}}