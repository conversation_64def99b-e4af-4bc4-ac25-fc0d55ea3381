/*
 * Auto-generated from GameManager
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { SocketManager } from './SocketManager.ts';
import { AuthManager } from './AuthManager.ts';
import { SoundManager } from './SoundManager.ts';
import { LocalizationManager } from './LocalizationManager.ts';
import { EventManager } from './EventManager.ts';
import { GameEvents } from './GameEvents.ts';

var w,k,P,b,S,A;c._RF.push({},"217b0YoQopDDo0f/tJf7zYZ","GameManager",void 0);var R=u.ccclass,I=u.property;e("GameManager",(w=R("GameManager"),k=I(h),w(((A=function(e){function t(){for(var t,a=arguments.length,o=new Array(a),i=0;i<a;i++)o[i]=arguments[i];return t=e.call.apply(e,[this].concat(o))||this,n(t,"soundManager",S,r(t)),t.socketManager=void 0,t.authManager=void 0,t.localizationManager=void 0,t.eventTarget=new l,t.currentRound=0,t.currentRoundId="",t.gameStatus=M.WAITING,t.timeRemains=0,t.balance=0,t.isConnected=!1,t.currentBet={totalAmount:0},t.canBet=!1,t.bettingHistory=[],t}a(t,e);var c=t.prototype;return c.mapSymbolToPlayer=function(e){if(!e)return null;var t=e.trim();if(!t)return null;switch(t.toUpperCase()){case"아":case"A":case"PLAYER_A":return G.A;case"파":case"P":case"PLAYER_P":return G.P;case"트":case"T":case"PLAYER_T":return G.T;default:return null}},t.getInstance=function(){return this._instance},c.start=function(){if(t._instance=this,this.privateInitializeManagers(),!this.privateSetupEventListeners())return console.log("[GameManager] Managers not ready, scheduling retry..."),void this.privateRetryInitialization(1);this.privateStartGame()},c.StartWithToken=function(e,t){if(console.log("[GameManager] Starting game with provided token..."),!e||0===e.trim().length)return console.error("[GameManager] Token parameter is required"),void this.eventTarget.emit("token-required",{message:"Token parameter is required to start the game",suggestion:"Please provide a valid authentication token"});this.privateStartGameWithToken(e.trim(),t)},c.startGame=function(){var e=i(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,console.log("[GameManager] Starting game..."),this.authManager.autoInitialize().success){e.next=8;break}return console.log("[GameManager] No URL token found"),this.eventTarget.emit("token-required",{message:"Please provide authentication token",suggestion:"Use startWithToken(token) method or add token to URL"}),p.instance.emit("token-required",{message:"Please provide authentication token",suggestion:"Use startWithToken(token) method or add token to URL"}),e.abrupt("return");case 8:return console.log("[GameManager] Authentication successful"),e.next=11,this.connectToGame();case 11:e.next=18;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("[GameManager] Failed to start game:",e.t0),this.eventTarget.emit("startup-failed",{error:e.t0.message}),p.instance.emit("startup-failed",{error:e.t0.message});case 18:case"end":return e.stop()}}),e,this,[[0,13]])