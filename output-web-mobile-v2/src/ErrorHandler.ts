/*
 * Auto-generated from ErrorHandler
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';

var l,f,p,E,d,y,g,w,v,H;i._RF.push({},"b4894oqJPtOx4u8Xf6LBezW","ErrorHandler",void 0);var b=s.ccclass,m=s.property;r("ErrorHandler",(l=b("ErrorHandler"),f=m(c),p=m(u),E=m(c),l(((H=function(r){function t(){for(var t,e=arguments.length,i=new Array(e),s=0;s<e;s++)i[s]=arguments[s];return(t=r.call.apply(r,[this].concat(i))||this).eventTarget=new a,t.errorHistory=[],t.maxErrorHistory=100,n(t,"errorPopup",g,o(t)),n(t,"errorMessageLabel",w,o(t)),n(t,"reconnectButton",v,o(t)),t}e(t,r),t.getInstance=function(){return this._instance};var i=t.prototype;return i.start=function(){t._instance=this,this.privateSetupErrorPopup(),console.log("[ErrorHandler] Initialized")},i.privateSetupErrorPopup=function(){this.errorPopup&&(this.errorPopup.active=!1),this.reconnectButton&&this.reconnectButton.on(c.EventType.TOUCH_END,this.onReconnectClicked,this)},i.HandleError=function(r){switch(console.error("[ErrorHandler] "+r.type.toUpperCase()+" Error:",r.message),this.addToHistory(r),r.type){case"connection":this.handleConnectionError(r);break;case"authentication":this.handleAuthenticationError(r);break;case"betting":this.handleBettingError(r);break;case"game":this.handleGameError(r);break;case"balance":this.handleBalanceError(r);break;default:this.handleGenericError(r)}this.eventTarget.emit("error-occurred",r)},i.handleConnectionError=function(r){var t,e=this;(((t={})[-1]=function(){return e.showError("연결 오류가 발생했습니다. 네트워크를 확인해주세요.",!0)},t[-2]=function(){return e.showError("서버 응답을 처리할 수 없습니다.",!1)},t[-3]=function(){return e.showError("연결이 끊어졌습니다. 재연결을 시도해주세요.",!0)},t[1006]=function(){return e.showError("서버 연결이 비정상적으로 종료되었습니다.",!0)},t[1011]=function(){return e.showError("서버에서 오류가 발생했습니다.",!0)},t)[r.code]||function(){return e.showError("연결 문제가 발생했습니다.",!0)})()},i.handleAuthenticationError=function(r){var t,e=this;(((t={})[401]=function(){e.showError("인증에 실패했습니다. 다시 로그인해주세요.",!1),e.redirectToLogin()},t[403]=function(){return e.showError("접근 권한이 없습니다.",!1)},t[419]=function(){e.showError("세션이 만료되었습니다. 다시 로그인해주세요.",!1),e.redirectToLogin()},t)[r.code]||function(){return e.showError("인증 오류가 발생했습니다.",!1)})()},i.handleBettingError=function(r){var t,e=this;(((t={})[1001]=function(){return e.showError("베팅 시간이 종료되었습니다.",!1)},t[1002]=function(){return e.showError("잔액이 부족합니다.",!1)},t[1003]=function(){return e.showError("최소 베팅 금액을 확인해주세요.",!1)},t[1004]=function(){return e.showError("최대 베팅 금액을 초과했습니다.",!1)},t[1005]=function(){return e.showError("이미 베팅이 완료되었습니다.",!1)},t)[r.code]||function(){return e.showError("베팅 처리 중 오류가 발생했습니다.",!1)})()},i.handleGameError=function(r){var t,e=this;(((t={})[2001]=function(){return e.showError("게임 라운드를 찾을 수 없습니다.",!1)},t[2002]=function(){return e.showError("게임 상태가 올바르지 않습니다.",!0)},t[2003]=function(){return e.showError("게임 데이터 동기화 오류입니다.",!0)},t)[r.code]||function(){return e.showError("게임 오류가 발생했습니다.",!0)})()},i.handleBalanceError=function(r){var t,e=this;(((t={})[3001]=function(){return e.showError("잔액 정보를 가져올 수 없습니다.",!0)},t[3002]=function(){return e.showError("잔액 업데이트에 실패했습니다.",!0)},t)[r.code]||function(){return e.showError("잔액 처리 중 오류가 발생했습니다.",!0)})()},i.handleGenericError=function(r){this.showError("오류가 발생했습니다: "+r.message,!1)},i.showError=function(r,t){var e=this;void 0===t&&(t=!1),this.errorMessageLabel&&(this.errorMessageLabel.string=r),this.reconnectButton&&(this.reconnectButton.active=t),this.errorPopup&&(this.errorPopup.active=!0),t||this.scheduleOnce((function(){e.HideError()}),5)},i.HideError=function(){this.errorPopup&&(this.errorPopup.active=!1)},i.onReconnectClicked=function(){this.HideError(),this.eventTarget.emit("reconnect-requested")},i.redirectToLogin=function(){this.eventTarget.emit("logout-required"),this.scheduleOnce((function(){"undefined"!=typeof window&&window.location&&(window.location.href="/minigame/landing/index.php")}),2)},i.addToHistory=function(r){this.errorHistory.unshift(r),this.errorHistory.length>this.maxErrorHistory&&(this.errorHistory=this.errorHistory.slice(0,this.maxErrorHistory))},i.GetErrorStats=function(){var r={total:this.errorHistory.length,byType:{},recent:this.errorHistory.slice(0,10),lastHour:0},t=new Date(Date.now()-36e5);return this.errorHistory.forEach((function(e){r.byType[e.type]=(r.byType[e.type]||0)+1,new Date(e.timestamp)>t&&r.lastHour++