/*
 * Auto-generated from AuthManager
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';

var r,s;n._RF.push({},"504f3rFk6NCKK7vR+UQW98b","AuthManager",void 0);var u=i.ccclass;t("AuthManager",u("AuthManager")(((s=function(t){function n(){for(var e,a=arguments.length,n=new Array(a),i=0;i<a;i++)n[i]=arguments[i];return(e=t.call.apply(t,[this].concat(n))||this)._authData={token:"",playerId:"",sessionId:"",balance:0,isAuthenticated:!1},e._config={defaultPlayerId:"player_1",sessionTimeout:36e5,defaultToken:"GEdR9jerB1iLv3EwwwWaQ94xRygrtRchNOqbqU4Ctq3%2btDYtl9MU%2bS5pYqku8mV41gx4KKEgk4gI7t4iQCjo5gsVvieLH7b7hX%2bCf7lZw%2b0%3d"},e}e(n,t),n.getInstance=function(){return this._instance};var i=n.prototype;return i.start=function(){n._instance=this,console.log("[AuthManager] Initialized")},i.extractTokenFromURL=function(){try{var t=new URLSearchParams(window.location.search).get("token");return t?(console.log("[AuthManager] Token extracted from URL"),decodeURIComponent(t)):(console.log("[AuthManager] No token found in URL parameters"),"")}catch(t){return console.error("[AuthManager] Failed to extract token from URL:",t),""}},i.initializeWithToken=function(t,e){return t&&0!==t.trim().length?(console.log("[AuthManager] Initializing with provided token"),this.initializeAuth(t.trim(),e||this._config.defaultPlayerId)):{success:!1,error:"Token parameter is required"}},i.extractTokenFromParameter=function(t){try{return t.startsWith("token=")?t.substring(6):t}catch(t){return console.error("[AuthManager] Failed to extract token from parameter:",t),""}},i.validateToken=function(t){if(!t||0===t.trim().length)return{valid:!1,error:"Token is empty"};if(t.length<50||t.length>200)return{valid:!1,error:"Token length is invalid"};try{return atob(t).length<10?{valid:!1,error:"Decoded token is too short"}:(console.log("[AuthManager] Token validation successful"),{valid:!0})}catch(t){return{valid:!1,error:"Token is not valid base64"}}},i.decodeToken=function(t){try{var e=atob(t);try{return{success:!0,data:JSON.parse(e)}}catch(t){return{success:!0,data:{raw:e,length:e.length,type:"binary"}}}}catch(t){return{success:!1,error:"Failed to decode token: "+t.message}}},i.initializeAuth=function(t,e){var a=this.validateToken(t);return a.valid?(this._authData.token=t,this._authData.playerId=e||"",this._authData.isAuthenticated=!1,console.log("[AuthManager] Authentication initialized"),{success:!0}):{success:!1,error:a.error}},i.setAuthenticationStatus=function(t,e,a){this._authData.isAuthenticated=t,e&&(this._authData.sessionId=e),void 0!==a&&(this._authData.balance=a),console.log("[AuthManager] Authentication status updated:",t)},i.updateBalance=function(t){this._authData.balance=t,console.log("[AuthManager] Balance updated:",t)},i.updateUserProfile=function(t,e){t&&(this._authData.userProfile=t,this._authData.playerId=t.id||this._authData.playerId,"number"==typeof t.balance&&(this._authData.balance=t.balance)),e&&(this._authData.bettingLimits=e),console.log("[AuthManager] User profile updated:",{playerId:this._authData.playerId,nickname:null==t?void 0:t.nickname,balance:this._authData.balance,currency:null==t?void 0:t.currency})},i.getAuthData=function(){return a({},this._authData)},i.getToken=function(){return this._authData.token},i.isAuthenticated=function(){return this._authData.isAuthenticated&&this._authData.token.length>0},i.getUserProfile=function(){return this._authData.userProfile},i.getBettingLimits=function(){return this._authData.bettingLimits},i.getBalance=function(){return this._authData.balance},i.getUserNickname=function(){var t;return(null==(t=this._authData.userProfile)?void 0:t.nickname)||this._authData.playerId||"Unknown"},i.clearAuth=function(){this._authData={token:"",playerId:"",sessionId:"",balance:0,isAuthenticated:!1},console.log("[AuthManager] Authentication data cleared")},i.getAuthHeaders=function(){return{Authorization:"Bearer "+this._authData.token,"X-Player-ID":this._authData.playerId,"X-Session-ID":this._authData.sessionId}},i.createAuthPayload=function(){return{token:this._authData.token,playerId:this._authData.playerId,sessionId:this._authData.sessionId,timestamp:(new Date).toISOString()}},i.autoInitialize=function(){var t=this.extractTokenFromURL();if(t)return console.log("[AuthManager] Using token from URL"),this.initializeAuth(t);if(console.log("[AuthManager] No token found in URL parameters, trying default token"),this._config.defaultToken){console.log("[AuthManager] Using default token");var e=decodeURIComponent(this._config.defaultToken),a=this.initializeAuth(e);return a.success?a:(console.error("[AuthManager] Default token validation failed:",a.error),{success:!1,error:"Default token validation failed: "+a.error})}return console.log("[AuthManager] No default token configured"),{success:!1,error:"No token found in URL and no default token configured"}},i.validateSession=function(){return!(!this._authData.token||!this._authData.isAuthenticated)&&this._authData.sessionId.length>0},i.refreshAuth=function(){var t=this;return new Promise((function(e){e(t.isAuthenticated())