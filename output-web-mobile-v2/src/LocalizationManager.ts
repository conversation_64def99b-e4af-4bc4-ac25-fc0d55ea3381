/*
 * Auto-generated from LocalizationManager
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { Singleton } from './Singleton.ts';
import { EventManager } from './EventManager.ts';

var l;n._RF.push({},"0c04eMOGAtI56Gdrgs7KNPn","LocalizationManager",void 0);var c=r.ccclass;a("LocalizationManager",c("LocalizationManager")(l=i(l=function(a){function n(){for(var e,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return(e=a.call.apply(a,[this].concat(n))||this)._currentLanguage="ko",e._translations={},e}e(n,a);var r=n.prototype;return r.onLoad=function(){this.initializeTranslations(),this.detectLanguageFromURL(),console.log("[LocalizationManager] Initialized with language: "+this._currentLanguage)},r.initializeTranslations=function(){this._translations={ko:{round_info_template:"{time}초 후 {round}회차 시작",round_info_static:"게임 시작",start_label:"시작",seconds_label:"초",date_format:"korean"},en:{round_info_template:"Game #{round} starts in {time}s",round_info_static:"Game starts",start_label:"Start",seconds_label:"s",date_format:"english"},"zh-cn":{round_info_template:"{time}秒后 第{round}期开始",round_info_static:"游戏开始",start_label:"开始",seconds_label:"秒",date_format:"chinese_simplified"},"zh-tw":{round_info_template:"{time}秒後 第{round}期開始",round_info_static:"遊戲開始",start_label:"開始",seconds_label:"秒",date_format:"chinese_traditional"},ja:{round_info_template:"{time}秒後 第{round}回開始",round_info_static:"ゲーム開始",start_label:"開始",seconds_label:"秒",date_format:"japanese"},th:{round_info_template:"อีก {time} วินาที เกมรอบที่ {round} เริ่ม",round_info_static:"เกมเริ่ม",start_label:"เริ่ม",seconds_label:"วินาที",date_format:"thai"},km:{round_info_template:"{time} វិនាទីទៀត ល្បែង {round} ចាប់ផ្តើម",round_info_static:"ល្បែងចាប់ផ្តើម",start_label:"ចាប់ផ្តើម",seconds_label:"វិនាទី",date_format:"khmer"},ms:{round_info_template:"Permainan #{round} bermula dalam {time}s",round_info_static:"Permainan bermula",start_label:"Mula",seconds_label:"s",date_format:"malay"},id:{round_info_template:"Game #{round} mulai dalam {time}d",round_info_static:"Game dimulai",start_label:"Mulai",seconds_label:"d",date_format:"indonesian"},vi:{round_info_template:"Ván #{round} bắt đầu",round_info_static:"Trò chơi bắt đầu",start_label:"Bắt đầu",seconds_label:"giây",date_format:"vietnamese"}}},r.detectLanguageFromURL=function(){var a=this;try{var e=new URLSearchParams(window.location.search).get("lang");if(console.log("[LocalizationManager] URL search params: "+window.location.search),console.log("[LocalizationManager] Lang parameter: "+e),e&&this.isValidLanguage(e)){var t=this._currentLanguage;this._currentLanguage=e,console.log("[LocalizationManager] Language detected from URL: "+this._currentLanguage),t!==this._currentLanguage&&setTimeout((function(){s.instance.emit(u.LANGUAGE_CHANGED,{oldLanguage:t,newLanguage:a._currentLanguage})}),100)}else console.log("[LocalizationManager] No valid language parameter found, using default: "+this._currentLanguage)}catch(a){console.error("[LocalizationManager] Failed to detect language from URL:",a)}},r.refreshLanguageFromURL=function(){this.detectLanguageFromURL()},r.isValidLanguage=function(a){return-1!==["ko","en","zh-cn","zh-tw","ja","th","km","ms","id","vi"].indexOf(a)},r.getText=function(a,e){var t,n=null==(t=this._translations[this._currentLanguage])?void 0:t[a];if(!n){var r;console.warn("[LocalizationManager] Translation not found for key: "+a+" in language: "+this._currentLanguage);var o=null==(r=this._translations.en)?void 0:r[a];return o?this.replaceParams(o,e):a}return this.replaceParams(n,e)},r.replaceParams=function(a,e){if(!e)return a;var t=a;for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];t=t.replace(new RegExp("\\{"+n+"\\}","g"),r.toString())}return t},r.padZero=function(a,e){void 0===e&&(e=2);for(var t=a.toString();t.length<e;)t="0"+t;return t},r.formatDate=function(a){var e=a.getUTCFullYear(),t=a.getUTCMonth()+1,n=a.getUTCDate();switch(this._currentLanguage){case"ko":return e+"년 "+t+"월 "+n+"일";case"en":return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t-1]+" "+n+", "+e;case"zh-cn":case"zh-tw":case"ja":return e+"年"+this.padZero(t)+"月"+this.padZero(n)+"日";case"th":return n+" "+["ม.ค.","ก.พ.","มี.ค.","เม.ย.","พ.ค.","มิ.ย.","ก.ค.","ส.ค.","ก.ย.","ต.ค.","พ.ย.","ธ.ค."][t-1]+" "+e;case"km":return n+" "+["មករា","កុម្ភៈ","មីនា","មេសា","ឧសភា","មិថុនា","កក្កដា","សីហា","កញ្ញា","តុលា","វិច្ឆិកា","ធ្នូ"][t-1]+" "+e;case"ms":return n+" "+["Jan","Feb","Mac","Apr","Mei","Jun","Jul","Ogos","Sep","Okt","Nov","Dis"][t-1]+" "+e;case"id":return n+" "+["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][t-1]+" "+e;case"vi":return"Ngày "+this.padZero(n)+"/"+this.padZero(t)+"/"+e;default:return e+"-"+this.padZero(t)+"-"+this.padZero(n)}},r.getCurrentLanguage=function(){return this._currentLanguage},r.setLanguage=function(a){if(this.isValidLanguage(a)){var e=this._currentLanguage;this._currentLanguage=a,console.log("[LocalizationManager] Language changed from "+e+" to: "+this._currentLanguage),s.instance.emit(u.LANGUAGE_CHANGED,{oldLanguage:e,newLanguage:this._currentLanguage})}else console.warn("[LocalizationManager] Invalid language: "+a)},r.getRoundInfoText=function(a,e){return this.getText("round_info_template",{time:a.toString(),round:e.toString()})},r.getRoundInfoStatic=function(){return this.getText("round_info_static")},r.getStartLabel=function(){return this.getText("start_label")},r.getSecondsLabel=function(){return this.getText("seconds_label")},t(n,null,[{key:"instance",get:function(){return n.instance}}]),n}(o))||l)||l);n._RF.pop()}}