/*
 * Auto-generated from TowerAnimationController
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';
import { GameEvents } from './GameEvents.ts';
import { EventManager } from './EventManager.ts';
import { TowerAnimation } from './TowerAnimation.ts';
import { DynamicRandomPhaseConfig } from './DynamicRandomPhaseConfig.ts';
import { PopupManager } from './PopupManager.ts';

var y,C,D,L,v,E,N,M,S,I,x,O,_,U,G,F,z,Y,B,H,k,V,W,j,J;l._RF.push({},"ef38dYr9fJGX4ILluY1eKgY","TowerAnimationController",void 0);var q=u.ccclass,K=u.property;t("TowerAnimationController",(y=q("TowerAnimationController"),C=K({tooltip:"Enable TowerAnimation system for 3-phase racing"}),D=K(A),L=K(A),v=K(A),E=K(c),N=K(d),M=K(d),S=K({tooltip:"Cloud starting Y position (moves down during race for parallax effect)"}),I=K({tooltip:"Cloud ending Y position"}),x=K({tooltip:"Enable cloud background animation during racing"}),O=K({tooltip:"Global race duration multiplier (for testing)"}),y((G=o((U=function(t){function o(){for(var o,i=arguments.length,r=new Array(i),a=0;a<i;a++)r[a]=arguments[a];return o=t.call.apply(t,[this].concat(r))||this,n(o,"enableTowerAnimations",G,e(o)),n(o,"towerAnimationA",F,e(o)),n(o,"towerAnimationP",z,e(o)),n(o,"towerAnimationT",Y,e(o)),n(o,"finalLineUIOpacity",B,e(o)),n(o,"cloudNode",H,e(o)),n(o,"confettiNode",k,e(o)),n(o,"cloudStartY",V,e(o)),n(o,"cloudEndY",W,e(o)),n(o,"enableCloudAnimation",j,e(o)),n(o,"raceDurationMultiplier",J,e(o)),o.firstPlayerOfRound=!1,o.isRacing=!1,o.raceStartTime=0,o.completedTowers=new Set,o.raceResults=[],o.countdownRaiseActive=!1,o.cloudTween=null,o}i(o,t);var l=o.prototype;return l.debugLog=function(){var t;T.PERFORMANCE.ENABLE_DEBUG_LOGGING&&(t=console).log.apply(t,arguments)},l.onLoad=function(){this.debugLog("[TowerAnimationController] onLoad() called - initializing controller"),this.setupTowerPlayers(),this.setupEventListeners(),this.setupTimingConfiguration(),this.debugLog("[TowerAnimationController] onLoad() completed")},l.setupTowerPlayers=function(){this.towerAnimationA&&this.towerAnimationA.setTowerPlayer(g.A),this.towerAnimationP&&this.towerAnimationP.setTowerPlayer(g.P),this.towerAnimationT&&this.towerAnimationT.setTowerPlayer(g.T),this.debugLog("[TowerAnimationController] Tower players configured")},l.setupEventListeners=function(){w.instance.on(f.TOWER_INDIVIDUAL_COMPLETE,this.handleTowerComplete,this),w.instance.on(f.STARTUP_POPUP_HIDDEN,this.onStartupPopupHidden,this),w.instance.on(f.GAME_RESET_UI,this.onGameResetUI,this),w.instance.on(f.ROUND_COUNTDOWN_STARTED,this.onRoundCountdownStarted,this),this.debugLog("[TowerAnimationController] Event listeners setup - listening for STARTUP_POPUP_HIDDEN, TOWER_INDIVIDUAL_COMPLETE, GAME_RESET_UI, and ROUND_COUNTDOWN_STARTED")},l.setupTimingConfiguration=function(){P(8,10);var t=b();this.debugLog("[TowerAnimationController] Timing configuration set to 8-10 second race duration"),this.debugLog("[TowerAnimationController] Current timing config:",{minDuration:t.minDuration/1e3+"s",maxDuration:t.maxDuration/1e3+"s",baseDuration:t.baseDuration/1e3+"s"}),this.debugLog("[TowerAnimationController] Race duration multiplier: "+this.raceDurationMultiplier+"x")},l.finalLineAnimationRound=function(){this.firstPlayerOfRound||(this.firstPlayerOfRound=!0,m(this.finalLineUIOpacity).to(.2,{opacity:255}).to(.2,{opacity:0}).union().start())},l.planRaceDurations=function(t){var o=this,i=new Map;if(!t||0===t.length)return i;var n=b(),e=[].concat(t).sort((function(t,o){return t.finalPosition-o.finalPosition