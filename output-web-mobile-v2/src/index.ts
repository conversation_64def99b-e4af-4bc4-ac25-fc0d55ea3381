/*
 * Auto-generated from index
 * Reverse engineered by cc-reverse
 */

import * as cc from 'cc';
import { SocketManager } from './SocketManager.ts';
import { AuthManager } from './AuthManager.ts';
import { GameManager } from './GameManager.ts';
import { <PERSON>rror<PERSON>andler } from './ErrorHandler.ts';
import { EventManager } from './EventManager.ts';
import { GameEvents } from './GameEvents.ts';

t._RF.push({},"ce384Y9AsVFNYpQD1KSoSRG","index",void 0),t._RF.pop()}}