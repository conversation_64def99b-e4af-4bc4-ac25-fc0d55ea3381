/*
 * Auto-generated from SpiteSkin
 * Reverse engineered by cc-reverse
 */

import { rollupPluginModLoBabelHelpers_js } from './rollupPluginModLoBabelHelpers.js';
import * as cc from 'cc';

var h,f,S,m,k,b,d,g,y,v,D,w,z;o._RF.push({},"149f6QPszFCQrLGehkDgbXp","SpiteSkin",void 0);var C=s.ccclass,F=s.property;s.menu,s.executeInEditMode,i("default",(h=C("SpriteSkin"),f=F(a),S=F(l),m=F(l),k=F(u),h((g=t((d=function(i){function t(){for(var t,e=arguments.length,o=new Array(e),s=0;s<e;s++)o[s]=arguments[s];return t=i.call.apply(i,[this].concat(o))||this,n(t,"item",g,r(t)),n(t,"isSprite",y,r(t)),n(t,"spriteSkin",v,r(t)),n(t,"spriteDefaultSkin",D,r(t)),n(t,"isColor",w,r(t)),n(t,"colorSkin",z,r(t)),t.idSkin=0,t}e(t,i);var o=t.prototype;return o.onLoad=function(){!this.item&&this.getComponent(a)&&(this.item=this.getComponent(a))},o.setSkin=function(i){if(i<0||i>this.countSkin())return p("wrong id skin "),void(this.spriteDefaultSkin&&(this.item.spriteFrame=this.spriteDefaultSkin));this.idSkin=i,this.isSprite&&this.item&&(this.spriteSkin[i]?this.item.spriteFrame=this.spriteSkin[i]:this.spriteDefaultSkin&&(this.item.spriteFrame=this.spriteDefaultSkin)),this.isColor&&this.item&&(this.item.color=this.colorSkin[i])},o.countSkin=function(){return this.isSprite&&this.item?this.spriteSkin.length:this.isColor&&this.item?this.colorSkin.length:void 0},o.randomSkin=function(){var i=this.getRandomInt(0,this.countSkin()-1);this.setSkin(i)},o.getRandomInt=function(i,t){return i=Math.ceil(i),t=Math.floor(t),Math.floor(Math.random()*(t-i+1))+i},t}(c)).prototype,"item",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=t(d.prototype,"isSprite",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),v=t(d.prototype,"spriteSkin",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),D=t(d.prototype,"spriteDefaultSkin",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=t(d.prototype,"isColor",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),z=t(d.prototype,"colorSkin",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),b=d))||b));o._RF.pop()}}